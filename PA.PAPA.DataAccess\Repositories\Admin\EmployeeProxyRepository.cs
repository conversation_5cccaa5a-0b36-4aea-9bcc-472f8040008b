﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.DataAccess.Repositories.Admin
{
    public class EmployeeProxyRepository : IEmployeeProxyRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public EmployeeProxyRepository(IConnectionFactory connectionFactory) {
            _connectionFactory = connectionFactory;
        }

        public async Task<IEnumerable<EmployeeProxyDTO>> ReadByParent(int parentEmployeeId) {
            var param = new DynamicParameters();
            param.Add("@ParentEmployeeId", parentEmployeeId);
            using (var results = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeProxySelectByParent", param, commandType: CommandType.StoredProcedure)) {
                return results.Read<EmployeeProxyDTO>().ToList();
            }
        }

        public async Task<EmployeeProxyDTO> Read(int id) {
            var param = new DynamicParameters();

            // pass in the organization id if we have one otherwise pass in 0 for all records
            param.Add("@EmployeeProxyId", id);

            using (var results = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeProxySelect", param, commandType: CommandType.StoredProcedure)) {
                return results.Read<EmployeeProxyDTO>().FirstOrDefault();
            }
        }

        public async Task<EmployeeProxyDTO> Create(EmployeeProxyDTO dto) {
            var param = new DynamicParameters();

            param.Add("@EmployeeProxyId", dto.EmployeeProxyId, null, ParameterDirection.InputOutput);
            param.Add("@EmployeeId", dto.EmployeeId);
            param.Add("@ParentEmployeeId", dto.ParentEmployeeId);

            using (var multiResult = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeProxyInsert", param, commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();
                dto.EmployeeProxyId = param.Get<int>("@EmployeeProxyId");
            }

            return await Read(dto.EmployeeProxyId);
        }

        public async Task<EmployeeProxyDTO> Update(EmployeeProxyDTO dto) {
            var param = new DynamicParameters();

            param.Add("@EmployeeProxyId", dto.EmployeeProxyId);
            param.Add("@EmployeeId", dto.EmployeeId);
            param.Add("@ParentEmployeeId", dto.ParentEmployeeId);

            using (var multiResult = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeProxyUpdate", param, commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();
            }

            return await Read(dto.EmployeeProxyId);
        }

        public void Dispose() {
            _connectionFactory.Dispose();
        }
    }
}
