﻿using System.Collections.Generic;
using MongoDB.Driver;
using PA.PAPA.API.Models;

namespace PA.PAPA.API.Services
{
    public class UserService
    {
        private readonly IMongoCollection<Employee> _users;

        #region snippet_UserServiceConstructor

        public UserService(IUserDatabaseSettings settings)
        {
            var client = new MongoClient(settings.ConnectionString);
            var database = client.GetDatabase(settings.DatabaseName);

            _users = database.GetCollection<Employee>(settings.UserCollectionName);
        }

        #endregion

        public List<Employee> Get()
        {
            return _users.Find(user => true).ToList();
        }

        public Employee Get(string id)
        {
            return _users.Find(user => user.Id == id).FirstOrDefault();
        }

        public Employee GetByEmployeeNumber(string employeeName)
        {
            return _users.Find(user => user.UserName.ToLower() == employeeName.ToLower()).FirstOrDefault();
        }

        public Employee Create(Employee user)
        {
            _users.InsertOne(user);
            return user;
        }

        public void Update(string id, Employee userIn)
        {
            _users.ReplaceOne(user => user.Id == id, userIn);
        }

        public void Remove(Employee userIn)
        {
            _users.DeleteOne(user => user.Id == userIn.Id);
        }

        public void Remove(string id)
        {
            _users.DeleteOne(user => user.Id == id);
        }
    }
}
