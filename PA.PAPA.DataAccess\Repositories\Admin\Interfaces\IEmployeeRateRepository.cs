﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.DataAccess.Repositories.Admin.Interfaces
{
    public interface IEmployeeRateRepository
    {
        Task<IEnumerable<EmployeeRateDTO>> ReadByEmployee(int employeeId);
        Task<EmployeeRateDTO> Read(int id);
        Task<EmployeeRateDTO> Create(EmployeeRateDTO dto);
        Task<EmployeeRateDTO> Update(EmployeeRateDTO dto);
    }
}
