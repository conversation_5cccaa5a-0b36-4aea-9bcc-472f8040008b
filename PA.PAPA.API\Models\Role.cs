﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

namespace BooksApi.Models
{
    public class Role
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        //public int ClaimId { get; set; }

        public bool IsActive { get; set; }

        [BsonElement("Description")]
        [JsonProperty("Description")]
        public string Description { get; set; }

        [BsonElement("Name")]
        [JsonProperty("Name")]
        public string Name { get; set; }

       public string NormalizedName { get; set; }

    }
}