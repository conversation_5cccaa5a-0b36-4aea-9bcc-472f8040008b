{"Version": 1, "Hash": "28aTpNjzB0gGLPnxxJC4td1k/gBXUzlKKlj1HKkrf/0=", "Source": "NT.BIRAOCR.API", "BasePath": "_content/NT.BIRAOCR.API", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "NT.BIRAOCR.API\\wwwroot", "Source": "NT.BIRAOCR.API", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "Pattern": "**"}], "Assets": [{"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\1nsoiovand-jp5omh6saj.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "ocrtest#[.{fingerprint=jp5omh6saj}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "67zvx56oz5", "Integrity": "8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.css"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\7vw31u85v4-4a7d5925mm.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "index#[.{fingerprint=4a7d5925mm}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dx4ttszv8r", "Integrity": "Mduy9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\index.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\7xt35krokb-20rbgnipeh.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "simple-upload-test#[.{fingerprint=20rbgnipeh}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simple-upload-test.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h1npferv8a", "Integrity": "5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simple-upload-test.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\9lnvaetr5y-7swnv70qx5.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "ocrtest#[.{fingerprint=7swnv70qx5}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rdjav4c7h8", "Integrity": "nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.js"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\aumneqwx1h-lq6hyijkef.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "beer-ocr-app#[.{fingerprint=lq6hyijkef}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s778wxzprx", "Integrity": "eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\e44z4ei2a0-5ipweew5fc.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm#[.{fingerprint=5ipweew5fc}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.js"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\fojtet4fwk-zh1ztznphx.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "ocrtest#[.{fingerprint=zh1ztznphx}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7m5k9zkgvw", "Integrity": "b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ndbhoi2p5z-bx607eef1i.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "main#[.{fingerprint=bx607eef1i}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2exsp76uyn", "Integrity": "Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\nxmnkq8grs-13ups3th7z.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "main#[.{fingerprint=13ups3th7z}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nm5zpj9rh5", "Integrity": "wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.js"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ple8t9otgc-lt4h0pu2ew.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm#[.{fingerprint=lt4h0pu2ew}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "57no0eg118", "Integrity": "jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\srkazlatko-5ipweew5fc.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "buttonTest#[.{fingerprint=5ipweew5fc}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.js"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\targijiu74-5ipweew5fc.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "simpleTest#[.{fingerprint=5ipweew5fc}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simpleTest.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simpleTest.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\u7jb7q6tsk-5ipweew5fc.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "upload-test#[.{fingerprint=5ipweew5fc}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\upload-test.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\upload-test.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ua6szec2ke-7ppuzqynnz.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "styles#[.{fingerprint=7ppuzqynnz}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l0o1dcou8x", "Integrity": "K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\styles.css"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\y5a729ybge-5ipweew5fc.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm#[.{fingerprint=5ipweew5fc}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.css"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\z1aoucubig-5ipweew5fc.gz", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "buttonTest#[.{fingerprint=5ipweew5fc}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "beer-ocr-app#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lq6hyijkef", "Integrity": "XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\beer-ocr-app.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "buttonTest#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\buttonTest.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.js", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "buttonTest#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\buttonTest.js"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\index.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4a7d5925mm", "Integrity": "5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "main#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bx607eef1i", "Integrity": "8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\main.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.js", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "main#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "13ups3th7z", "Integrity": "cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\main.js"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.css", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\mainForm.css"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lt4h0pu2ew", "Integrity": "qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\mainForm.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html.bak", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm.html#[.{fingerprint}]?.bak", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z6m5epkocb", "Integrity": "Ze6vaspCi2PrUT87dEqck+ymuqMKLx1xLIely4az75w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\mainForm.html.bak"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.js", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\mainForm.js"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.css", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "ocrtest#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jp5omh6saj", "Integrity": "50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ocrtest.css"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "ocrtest#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zh1ztznphx", "Integrity": "qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ocrtest.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.js", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "ocrtest#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7swnv70qx5", "Integrity": "SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ocrtest.js"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simple-upload-test.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "simple-upload-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "20rbgnipeh", "Integrity": "concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\simple-upload-test.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simpleTest.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "simpleTest#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\simpleTest.html"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\styles.css", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7ppuzqynnz", "Integrity": "m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\styles.css"}, {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\upload-test.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "upload-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\upload-test.html"}], "Endpoints": [{"Route": "beer-ocr-app.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\aumneqwx1h-lq6hyijkef.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001872659176"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "533"}, {"Name": "ETag", "Value": "\"eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU="}]}, {"Route": "beer-ocr-app.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1173"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU="}]}, {"Route": "beer-ocr-app.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\aumneqwx1h-lq6hyijkef.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "533"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo="}]}, {"Route": "beer-ocr-app.lq6hyijkef.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\aumneqwx1h-lq6hyijkef.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001872659176"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "533"}, {"Name": "ETag", "Value": "\"eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lq6hyijkef"}, {"Name": "label", "Value": "beer-ocr-app.html"}, {"Name": "integrity", "Value": "sha256-XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU="}]}, {"Route": "beer-ocr-app.lq6hyijkef.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1173"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lq6hyijkef"}, {"Name": "label", "Value": "beer-ocr-app.html"}, {"Name": "integrity", "Value": "sha256-XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU="}]}, {"Route": "beer-ocr-app.lq6hyijkef.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\aumneqwx1h-lq6hyijkef.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "533"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lq6hyijkef"}, {"Name": "label", "Value": "beer-ocr-app.html.gz"}, {"Name": "integrity", "Value": "sha256-eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo="}]}, {"Route": "buttonTest.5ipweew5fc.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\z1aoucubig-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "buttonTest.html"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.5ipweew5fc.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "buttonTest.html"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.5ipweew5fc.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\z1aoucubig-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "buttonTest.html.gz"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.5ipweew5fc.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\srkazlatko-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "buttonTest.js"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.5ipweew5fc.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "buttonTest.js"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.5ipweew5fc.js.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\srkazlatko-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "buttonTest.js.gz"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\z1aoucubig-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\z1aoucubig-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\srkazlatko-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.js.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\srkazlatko-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "index.4a7d5925mm.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\7vw31u85v4-4a7d5925mm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000502765209"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1988"}, {"Name": "ETag", "Value": "\"Mduy9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4a7d5925mm"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q="}]}, {"Route": "index.4a7d5925mm.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8810"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4a7d5925mm"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q="}]}, {"Route": "index.4a7d5925mm.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\7vw31u85v4-4a7d5925mm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1988"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Mduy9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4a7d5925mm"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-M<PERSON>y9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4="}]}, {"Route": "index.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\7vw31u85v4-4a7d5925mm.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000502765209"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1988"}, {"Name": "ETag", "Value": "\"Mduy9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q="}]}, {"Route": "index.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8810"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q="}]}, {"Route": "index.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\7vw31u85v4-4a7d5925mm.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1988"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Mduy9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M<PERSON>y9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4="}]}, {"Route": "main.13ups3th7z.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\nxmnkq8grs-13ups3th7z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000922509225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1083"}, {"Name": "ETag", "Value": "\"wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "13ups3th7z"}, {"Name": "label", "Value": "main.js"}, {"Name": "integrity", "Value": "sha256-cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y="}]}, {"Route": "main.13ups3th7z.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4265"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "13ups3th7z"}, {"Name": "label", "Value": "main.js"}, {"Name": "integrity", "Value": "sha256-cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y="}]}, {"Route": "main.13ups3th7z.js.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\nxmnkq8grs-13ups3th7z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1083"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "13ups3th7z"}, {"Name": "label", "Value": "main.js.gz"}, {"Name": "integrity", "Value": "sha256-wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw="}]}, {"Route": "main.bx607eef1i.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ndbhoi2p5z-bx607eef1i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001123595506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "ETag", "Value": "\"Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bx607eef1i"}, {"Name": "label", "Value": "main.html"}, {"Name": "integrity", "Value": "sha256-8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk="}]}, {"Route": "main.bx607eef1i.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3160"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bx607eef1i"}, {"Name": "label", "Value": "main.html"}, {"Name": "integrity", "Value": "sha256-8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk="}]}, {"Route": "main.bx607eef1i.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ndbhoi2p5z-bx607eef1i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bx607eef1i"}, {"Name": "label", "Value": "main.html.gz"}, {"Name": "integrity", "Value": "sha256-Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E="}]}, {"Route": "main.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ndbhoi2p5z-bx607eef1i.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001123595506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "ETag", "Value": "\"Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk="}]}, {"Route": "main.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3160"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk="}]}, {"Route": "main.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ndbhoi2p5z-bx607eef1i.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E="}]}, {"Route": "main.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\nxmnkq8grs-13ups3th7z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000922509225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1083"}, {"Name": "ETag", "Value": "\"wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y="}]}, {"Route": "main.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4265"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y="}]}, {"Route": "main.js.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\nxmnkq8grs-13ups3th7z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1083"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw="}]}, {"Route": "mainForm.5ipweew5fc.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\y5a729ybge-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "mainForm.css"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.5ipweew5fc.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "mainForm.css"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.5ipweew5fc.css.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\y5a729ybge-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "mainForm.css.gz"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.5ipweew5fc.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\e44z4ei2a0-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "mainForm.js"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.5ipweew5fc.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "mainForm.js"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.5ipweew5fc.js.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\e44z4ei2a0-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "mainForm.js.gz"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\y5a729ybge-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.css.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\y5a729ybge-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ple8t9otgc-lt4h0pu2ew.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000492125984"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2031"}, {"Name": "ETag", "Value": "\"jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw="}]}, {"Route": "mainForm.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5897"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw="}]}, {"Route": "mainForm.html.bak", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html.bak", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5887"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Ze6vaspCi2PrUT87dEqck+ymuqMKLx1xLIely4az75w=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ze6vaspCi2PrUT87dEqck+ymuqMKLx1xLIely4az75w="}]}, {"Route": "mainForm.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ple8t9otgc-lt4h0pu2ew.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2031"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE="}]}, {"Route": "mainForm.html.z6m5epkocb.bak", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html.bak", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5887"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Ze6vaspCi2PrUT87dEqck+ymuqMKLx1xLIely4az75w=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z6m5epkocb"}, {"Name": "label", "Value": "mainForm.html.bak"}, {"Name": "integrity", "Value": "sha256-Ze6vaspCi2PrUT87dEqck+ymuqMKLx1xLIely4az75w="}]}, {"Route": "mainForm.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\e44z4ei2a0-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.js.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\e44z4ei2a0-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.lt4h0pu2ew.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ple8t9otgc-lt4h0pu2ew.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000492125984"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2031"}, {"Name": "ETag", "Value": "\"jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lt4h0pu2ew"}, {"Name": "label", "Value": "mainForm.html"}, {"Name": "integrity", "Value": "sha256-qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw="}]}, {"Route": "mainForm.lt4h0pu2ew.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5897"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lt4h0pu2ew"}, {"Name": "label", "Value": "mainForm.html"}, {"Name": "integrity", "Value": "sha256-qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw="}]}, {"Route": "mainForm.lt4h0pu2ew.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ple8t9otgc-lt4h0pu2ew.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2031"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lt4h0pu2ew"}, {"Name": "label", "Value": "mainForm.html.gz"}, {"Name": "integrity", "Value": "sha256-jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE="}]}, {"Route": "ocrtest.7swnv70qx5.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\9lnvaetr5y-7swnv70qx5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000226757370"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4409"}, {"Name": "ETag", "Value": "\"nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7swnv70qx5"}, {"Name": "label", "Value": "ocrtest.js"}, {"Name": "integrity", "Value": "sha256-SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0="}]}, {"Route": "ocrtest.7swnv70qx5.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16388"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7swnv70qx5"}, {"Name": "label", "Value": "ocrtest.js"}, {"Name": "integrity", "Value": "sha256-SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0="}]}, {"Route": "ocrtest.7swnv70qx5.js.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\9lnvaetr5y-7swnv70qx5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4409"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7swnv70qx5"}, {"Name": "label", "Value": "ocrtest.js.gz"}, {"Name": "integrity", "Value": "sha256-nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc="}]}, {"Route": "ocrtest.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\1nsoiovand-jp5omh6saj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000475737393"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2101"}, {"Name": "ETag", "Value": "\"8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ="}]}, {"Route": "ocrtest.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7773"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ="}]}, {"Route": "ocrtest.css.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\1nsoiovand-jp5omh6saj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M="}]}, {"Route": "ocrtest.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\fojtet4fwk-zh1ztznphx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000493827160"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2024"}, {"Name": "ETag", "Value": "\"b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0="}]}, {"Route": "ocrtest.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5887"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0="}]}, {"Route": "ocrtest.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\fojtet4fwk-zh1ztznphx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2024"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ="}]}, {"Route": "ocrtest.jp5omh6saj.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\1nsoiovand-jp5omh6saj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000475737393"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2101"}, {"Name": "ETag", "Value": "\"8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jp5omh6saj"}, {"Name": "label", "Value": "ocrtest.css"}, {"Name": "integrity", "Value": "sha256-50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ="}]}, {"Route": "ocrtest.jp5omh6saj.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7773"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jp5omh6saj"}, {"Name": "label", "Value": "ocrtest.css"}, {"Name": "integrity", "Value": "sha256-50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ="}]}, {"Route": "ocrtest.jp5omh6saj.css.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\1nsoiovand-jp5omh6saj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jp5omh6saj"}, {"Name": "label", "Value": "ocrtest.css.gz"}, {"Name": "integrity", "Value": "sha256-8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M="}]}, {"Route": "ocrtest.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\9lnvaetr5y-7swnv70qx5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000226757370"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4409"}, {"Name": "ETag", "Value": "\"nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0="}]}, {"Route": "ocrtest.js", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16388"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0="}]}, {"Route": "ocrtest.js.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\9lnvaetr5y-7swnv70qx5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4409"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc="}]}, {"Route": "ocrtest.zh1ztznphx.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\fojtet4fwk-zh1ztznphx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000493827160"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2024"}, {"Name": "ETag", "Value": "\"b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh1ztznphx"}, {"Name": "label", "Value": "ocrtest.html"}, {"Name": "integrity", "Value": "sha256-qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0="}]}, {"Route": "ocrtest.zh1ztznphx.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5887"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh1ztznphx"}, {"Name": "label", "Value": "ocrtest.html"}, {"Name": "integrity", "Value": "sha256-qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0="}]}, {"Route": "ocrtest.zh1ztznphx.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\fojtet4fwk-zh1ztznphx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2024"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh1ztznphx"}, {"Name": "label", "Value": "ocrtest.html.gz"}, {"Name": "integrity", "Value": "sha256-b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ="}]}, {"Route": "simple-upload-test.20rbgnipeh.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\7xt35krokb-20rbgnipeh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001015228426"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "984"}, {"Name": "ETag", "Value": "\"5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "20rbgnipeh"}, {"Name": "label", "Value": "simple-upload-test.html"}, {"Name": "integrity", "Value": "sha256-concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0="}]}, {"Route": "simple-upload-test.20rbgnipeh.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simple-upload-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2877"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "20rbgnipeh"}, {"Name": "label", "Value": "simple-upload-test.html"}, {"Name": "integrity", "Value": "sha256-concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0="}]}, {"Route": "simple-upload-test.20rbgnipeh.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\7xt35krokb-20rbgnipeh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "984"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "20rbgnipeh"}, {"Name": "label", "Value": "simple-upload-test.html.gz"}, {"Name": "integrity", "Value": "sha256-5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI="}]}, {"Route": "simple-upload-test.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\7xt35krokb-20rbgnipeh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001015228426"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "984"}, {"Name": "ETag", "Value": "\"5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0="}]}, {"Route": "simple-upload-test.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simple-upload-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2877"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0="}]}, {"Route": "simple-upload-test.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\7xt35krokb-20rbgnipeh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "984"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI="}]}, {"Route": "simpleTest.5ipweew5fc.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\targijiu74-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "simpleTest.html"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "simpleTest.5ipweew5fc.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simpleTest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "simpleTest.html"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "simpleTest.5ipweew5fc.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\targijiu74-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "simpleTest.html.gz"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "simpleTest.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\targijiu74-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "simpleTest.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simpleTest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "simpleTest.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\targijiu74-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "styles.7ppuzqynnz.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ua6szec2ke-7ppuzqynnz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001675041876"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "596"}, {"Name": "ETag", "Value": "\"K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ppuzqynnz"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg="}]}, {"Route": "styles.7ppuzqynnz.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1599"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ppuzqynnz"}, {"Name": "label", "Value": "styles.css"}, {"Name": "integrity", "Value": "sha256-m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg="}]}, {"Route": "styles.7ppuzqynnz.css.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ua6szec2ke-7ppuzqynnz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "596"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ppuzqynnz"}, {"Name": "label", "Value": "styles.css.gz"}, {"Name": "integrity", "Value": "sha256-K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8="}]}, {"Route": "styles.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ua6szec2ke-7ppuzqynnz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001675041876"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "596"}, {"Name": "ETag", "Value": "\"K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg="}]}, {"Route": "styles.css", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1599"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg="}]}, {"Route": "styles.css.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\ua6szec2ke-7ppuzqynnz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "596"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8="}]}, {"Route": "upload-test.5ipweew5fc.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\u7jb7q6tsk-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "upload-test.html"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "upload-test.5ipweew5fc.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\upload-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "upload-test.html"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "upload-test.5ipweew5fc.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\u7jb7q6tsk-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "label", "Value": "upload-test.html.gz"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "upload-test.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\u7jb7q6tsk-5ipweew5fc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "upload-test.html", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\upload-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "upload-test.html.gz", "AssetFile": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\Debug\\net9.0\\compressed\\u7jb7q6tsk-5ipweew5fc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}]}