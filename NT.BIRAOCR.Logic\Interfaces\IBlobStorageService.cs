/*
 * © 2025 <PERSON> | SmartIT. All Rights Reserved.
 * https://johnkocer.github.io
 * Version: 1.0.0 Date: 05-21-2025
 */

using Microsoft.AspNetCore.Http;

namespace NT.BIRAOCR.Logic.Interfaces
{
    public interface IBlobStorageService
    {
        Task<string> UploadFileAsync(IFormFile file);
        Task<bool> DeleteFileAsync(string filePath);
        Task<byte[]> DownloadFileAsync(string filePath);
    }
}
