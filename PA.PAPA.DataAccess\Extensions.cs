﻿using System;
using System.Collections.Generic;
using System.Text;

namespace PA.PAPA.DataAccess
{
    public static class Extensions
    {

        #region DBValue        

        /// handles converting null to DBNull.Value         

        public static object DBValue(this decimal? value) {
            if (value.HasValue) {
                return value.Value;
            }
            else {
                return DBNull.Value;
            }
        }

        public static object DBValue(this int? value) {
            if (value.HasValue) {
                return value.Value;
            }
            else {
                return DBNull.Value;
            }
        }

        public static object DBValue(this string value) {
            if (value != null) {
                return value;
            }
            else {
                return DBNull.Value;
            }
        }

        #endregion

    }
}
