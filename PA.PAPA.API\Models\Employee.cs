﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

public class Employee
    {
       //public int Id { get; set; }
        //public int UserId { get; set; }

        // public string Name { get; set; }
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }
        // public int UserId { get; set; }

        // [BsonElement("Name")]
        // [JsonProperty("Name")]
        public string Name { get; set; }
        
        [BsonElement("UserName")]
        [JsonProperty("UserName")]
        public string UserName { get; set; }

        public string Email { get; set; }
        [BsonElement("EmployeeNumber")]
        [JsonProperty("EmployeeNumber")]
    public string EmployeeNumber { get; set; }
    }