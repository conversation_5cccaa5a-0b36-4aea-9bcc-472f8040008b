-- MySQL Database Setup for OsirTechA<PERSON>yzer

-- Create Database (if not exists)
CREATE DATABASE IF NOT EXISTS OsirTechAnalyzer;

USE OsirTechAnalyzer;

-- Create AnalysisRequests Table
CREATE TABLE IF NOT EXISTS AnalysisRequests (
    RequestId INT AUTO_INCREMENT PRIMARY KEY,
    CustomerName VARCHAR(100) NOT NULL,
    CustomerNumber VARCHAR(20) NOT NULL,
    RequestDate DATETIME NOT NULL,
    ImagePath VARCHAR(500) NOT NULL,
    Location VARCHAR(100) NOT NULL,
    MenuItem VARCHAR(100) NOT NULL,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    Status VARCHAR(20) DEFAULT 'Pending'
);

-- Create Stored Procedure for Submitting Analysis Requests
DROP PROCEDURE IF EXISTS sp_SubmitAnalysisRequest;

DELIMITER //
CREATE PROCEDURE sp_SubmitAnalysisRequest(
    IN p_CustomerName VARCHAR(100),
    IN p_CustomerNumber VARCHAR(20),
    IN p_RequestDate DATETIME,
    IN p_ImagePath VARCHAR(500),
    IN p_Location VARCHAR(100),
    IN p_MenuItem VARCHAR(100)
)
BEGIN
    INSERT INTO AnalysisRequests 
        (CustomerName, CustomerNumber, RequestDate, ImagePath, Location, MenuItem)
    VALUES 
        (p_CustomerName, p_CustomerNumber, p_RequestDate, p_ImagePath, p_Location, p_MenuItem);
    
    SELECT LAST_INSERT_ID() AS RequestId;
END //
DELIMITER ;

-- Optional: Create a separate users table for authentication
CREATE TABLE IF NOT EXISTS Users (
    UserId INT AUTO_INCREMENT PRIMARY KEY,
    Username VARCHAR(50) NOT NULL UNIQUE,
    Email VARCHAR(100) NOT NULL UNIQUE,
    PasswordHash VARCHAR(128) NOT NULL,
    PasswordSalt VARCHAR(128) NOT NULL,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    LastLoginDate DATETIME NULL,
    IsActive BOOLEAN DEFAULT TRUE
);
