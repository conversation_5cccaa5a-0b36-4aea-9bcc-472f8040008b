﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.Logic.Admin.Interfaces
{
    public interface IEmployeeProxyLogic : IDisposable
    {
        Task<IEnumerable<EmployeeProxyDTO>> ReadByParent(int parentEmployeeId);
        Task<EmployeeProxyDTO> Read(int id);
        Task<EmployeeProxyDTO> Create(EmployeeProxyDTO dto);
        Task<EmployeeProxyDTO> Update(EmployeeProxyDTO dto);
    }
}