[{"ContainingType": "NT.BIRAOCR.API.Program+<>c", "Method": "<Main>b__0_3", "RelativePath": "", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "NT.BIRAOCR.API.Controllers.AnalysisController", "Method": "SubmitAnalysis", "RelativePath": "api/Analysis/submit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "NT.BIRAOCR.DTO.AnalysisRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "NT.BIRAOCR.API.Controllers.AnalysisController", "Method": "UploadImage", "RelativePath": "api/Analysis/upload-image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "NT.BIRAOCR.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "login", "Type": "NT.BIRAOCR.DTO.UserLoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "NT.BIRAOCR.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registration", "Type": "NT.BIRAOCR.DTO.UserRegistrationDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "NT.BIRAOCR.API.Controllers.AuthController", "Method": "ValidateToken", "RelativePath": "api/Auth/validate-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "NT.BIRAOCR.API.Controllers.OCRController", "Method": "ExtractText", "RelativePath": "api/OCR/extract", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "image", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "NT.BIRAOCR.API.Controllers.OCRController", "Method": "ExtractTextTest", "RelativePath": "api/OCR/extractTest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "image", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "prompt", "Type": "System.String", "IsRequired": false}, {"Name": "userText", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}]