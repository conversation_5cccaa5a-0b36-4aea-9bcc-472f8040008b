﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.DataAccess.Repositories.Admin.Interfaces
{
    public interface IEmployeeProxyRepository
    {
        Task<IEnumerable<EmployeeProxyDTO>> ReadByParent(int parentEmployeeId);
        Task<EmployeeProxyDTO> Read(int id);
        Task<EmployeeProxyDTO> Create(EmployeeProxyDTO dto);
        Task<EmployeeProxyDTO> Update(EmployeeProxyDTO dto);
    }
}
