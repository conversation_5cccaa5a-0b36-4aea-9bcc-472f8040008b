/* mainForm.css - Web version styling for BiraOCRApp */

/* Base styles and reset */
*, *::before, *::after {
  box-sizing: border-box;
}

:root {
  --primary-color: #3498db;
  --primary-hover: #2980b9;
  --danger-color: #e74c3c;
  --danger-hover: #c0392b;
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --neutral-color: #ecf0f1;
  --dark-color: #2c3e50;
  --light-color: #f5f5f5;
  --text-color: #333;
  --text-light: #fff;
  --border-radius: 4px;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body, html {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  color: var(--text-color);
  background-color: var(--light-color);
  height: 100%;
  line-height: 1.6;
}

/* Main layout and containers */
.app-container {
  width: 90%;
  max-width: 800px;
  margin: 0 auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background-color: white;
  box-shadow: var(--shadow);
}

/* Header and menu styling - equivalent to MenuStrip in Windows Forms */
.app-header {
  padding: 0.5rem 0;
  border-bottom: 1px solid #ddd;
}

.main-menu {
  background-color: var(--neutral-color);
  border-radius: var(--border-radius);
}

.main-menu ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
  display: flex;
}

.main-menu > ul > li {
  position: relative;
  margin-right: 0.5rem;
}

.main-menu ul li a {
  display: block;
  padding: 0.5rem 1rem;
  text-decoration: none;
  color: var(--text-color);
  transition: var(--transition);
}

.main-menu > ul > li > a:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.submenu {
  display: none;
  position: absolute;
  background-color: white;
  min-width: 160px;
  box-shadow: var(--shadow);
  z-index: 10;
  border-radius: var(--border-radius);
}

.main-menu li:hover .submenu {
  display: block;
}

.submenu li a {
  padding: 0.8rem 1rem;
}

.submenu li a:hover {
  background-color: var(--neutral-color);
}

/* Toolbar styling - equivalent to the top buttons in WinForms */
.toolbar {
  display: flex;
  align-items: center;
  padding: 1rem 0;
  gap: 1rem;
}

/* Button styles - equivalent to Button controls in WinForms */
.primary-button, .exit-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: bold;
  transition: var(--transition);
  font-size: 1rem;
}

.primary-button {
  background-color: var(--primary-color);
  color: var(--text-light);
}

.primary-button:hover {
  background-color: var(--primary-hover);
}

.exit-button {
  background-color: var(--danger-color);
  color: var(--text-light);
  margin-left: auto;
}

.exit-button:hover {
  background-color: var(--danger-hover);
}

/* Progress Bar - equivalent to ProgressBar in WinForms */
.progress-bar {
  flex-grow: 1;
  height: 25px;
  background-color: var(--neutral-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  display: none;
}

.progress-indicator {
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
  background-size: 200% 100%;
  animation: progress-animation 1.5s infinite linear;
}

@keyframes progress-animation {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

/* Status label - equivalent to statusLabel in WinForms */
.status-label {
  padding: 0.75rem 0;
  font-weight: bold;
  color: var(--primary-color);
  min-height: 50px;
}

/* Results Grid - equivalent to resultsGrid in WinForms */
.grid-container {
  flex-grow: 1;
  margin-bottom: 1rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.results-grid {
  width: 100%;
  border-collapse: collapse;
}

.results-grid th, .results-grid td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.results-grid th {
  background-color: var(--neutral-color);
  font-weight: bold;
}

.results-grid tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

.results-grid tr:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Footer */
.app-footer {
  padding: 1rem 0;
  text-align: center;
  color: #777;
  font-size: 0.8rem;
  margin-top: auto;
  border-top: 1px solid #ddd;
}

/* Modal dialogs for About and Processing status */
.modal {
  display: none;
  position: fixed;
  z-index: 100;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  margin: 15% auto;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  max-width: 500px;
  animation: modalFadeIn 0.3s;
}

.processing-modal .modal-content {
  text-align: center;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.close-button {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
}

/* Spinner for processing modal */
.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--neutral-color);
  border-top: 5px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status indicator colors */
.status-success {
  color: var(--success-color);
}

.status-processing {
  color: var(--primary-color);
}

.status-error {
  color: var(--danger-color);
}

/* OCR Prompt Configuration */
.ocr-config-section {
  margin: 1rem 0;
  padding: 1rem;
  background-color: var(--neutral-color);
  border-radius: var(--border-radius);
}

.ocr-config-section h3 {
  margin-top: 0;
  color: var(--dark-color);
  font-size: 1.1rem;
}

.ocr-prompt-input {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 0.9rem;
  resize: vertical;
  margin-bottom: 0.8rem;
  min-height: 150px;
}

.user-text-input {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 0.9rem;
  resize: vertical;
  margin-bottom: 0.8rem;
  min-height: 80px;
}

.secondary-button {
  padding: 0.5rem 1rem;
  background-color: var(--neutral-color);
  color: var(--dark-color);
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.secondary-button:hover {
  background-color: #ddd;
}

/* Responsive design */
@media (max-width: 600px) {
  .app-container {
    width: 100%;
    padding: 0.5rem;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .exit-button {
    margin-left: 0;
    margin-top: 0.5rem;
  }
  
  .modal-content {
    width: 90%;
    margin-top: 20%;
  }
}

/* Focus and accessibility styles */
button:focus, a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.visually-hidden {
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
}