{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=BIRAOCR;User Id=sa;Password=YourPassword;TrustServerCertificate=True;"}, "Jwt": {"Key": "YourSecretKeyForAuthenticationOfApplicationBIRAOCR", "Issuer": "BIRAOCR", "Audience": "BIRAOCR", "ExpiryMinutes": 10080}, "Storage": {"UploadDirectory": "uploads"}, "OpenAI": {"ApiKey": "********************************************************************************************************************************************************************"}, "Serilog": {"Using": ["Serilog.Sinks.RollingFile", "Serilog.Sinks.Async"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning"}}, "WriteTo": [{"Name": "Async", "Args": {"configure": [{"Name": "RollingFile", "Args": {"pathFormat": "Logs/log-{Date}.log"}}]}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "NT_BIRAOCR_API_V1"}}}