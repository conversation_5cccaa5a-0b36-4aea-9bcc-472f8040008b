/*
 * © 2025 <PERSON> | SmartIT. All Rights Reserved.
 * https://johnkocer.github.io
 * Version: 1.0.0 Date: 05-21-2025
 */

const baseUrl = 'https://localhost:5001'; // Change if needed
let token = localStorage.getItem('token') || null;

function showResponse(id, data) {
    const el = document.getElementById(id);
    el.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
    el.style.display = 'block';
}

function updateUI() {
    if (token) {
        document.getElementById('auth-section').style.display = 'none';
        document.getElementById('app-section').style.display = '';
        document.getElementById('current-token').textContent = token.substring(0, 20) + '...';
    } else {
        document.getElementById('auth-section').style.display = '';
        document.getElementById('app-section').style.display = 'none';
        document.getElementById('current-token').textContent = '';
    }
}

// Register
document.getElementById('register-form').onsubmit = async function (e) {
    e.preventDefault();
    const payload = {
        username: document.getElementById('register-username').value,
        email: document.getElementById('register-email').value,
        password: document.getElementById('register-password').value
    };
    const res = await fetch(`${baseUrl}/api/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
    });
    showResponse('register-response', await res.json());
};

// Login
document.getElementById('login-form').onsubmit = async function (e) {
    e.preventDefault();
    const payload = {
        username: document.getElementById('login-username').value,
        password: document.getElementById('login-password').value
    };
    const res = await fetch(`${baseUrl}/api/Auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
    });
    const data = await res.json();
    showResponse('login-response', data);
    if (data.token) {
        token = data.token;
        localStorage.setItem('token', token);
        updateUI();
    }
};

// Logout
document.getElementById('logout-btn').onclick = function () {
    token = null;
    localStorage.removeItem('token');
    updateUI();
};

// Submit Analysis
document.getElementById('analysis-form').onsubmit = async function (e) {
    e.preventDefault();
    if (!token) return;
    const payload = {
        customerName: document.getElementById('customerName').value,
        customerNumber: document.getElementById('customerNumber').value,
        requestDate: document.getElementById('requestDate').value,
        imagePath: document.getElementById('imagePath').value,
        location: document.getElementById('location').value,
        menuItem: document.getElementById('menuItem').value
    };
    const res = await fetch(`${baseUrl}/api/analysis/submit`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(payload)
    });
    showResponse('analysis-response', await res.json());
};

// Upload Image
document.getElementById('upload-form').onsubmit = async function (e) {
    e.preventDefault();
    if (!token) return;
    const fileInput = document.getElementById('image-file');
    if (!fileInput.files.length) return;
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    const res = await fetch(`${baseUrl}/api/Analysis/upload-image`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` },
        body: formData
    });
    showResponse('upload-response', await res.json());
};

// Validate Token
document.getElementById('validate-form').onsubmit = async function (e) {
    e.preventDefault();
    if (!token) return;
    const res = await fetch(`${baseUrl}/api/auth/validate-token`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(token)
    });
    showResponse('validate-response', await res.json());
};

// On load
updateUI();
