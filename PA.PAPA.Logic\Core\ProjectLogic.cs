﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using FluentValidation;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using PA.PAPA.DataAccess.Repositories.Core.Interfaces;
using PA.PAPA.DTO.Core;
using PA.PAPA.Logic.Core.Interfaces;

namespace PA.PAPA.Logic.Core
{
    public class ProjectLogic : IProjectLogic
    {
        private readonly IProjectRepository _repo;

        public ProjectLogic(IProjectRepository ProjectRepository) {
            _repo = ProjectRepository;
        }

        
        public async Task<ProjectDTO> Read(int id) {
            return await _repo.Read(id);
        }

        public async Task<ProjectDTO> Create(ProjectDTO dto) {

            Validate(dto);

            return await _repo.Create(dto);

        }

        public async Task<ProjectDTO> Update(ProjectDTO dto) {

            if (dto == null) {
                var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", "Item not set") };
                throw new FluentValidation.ValidationException("", errors);
            }

            var existing = await _repo.Read(dto.ProjectId);
            if (existing == default(ProjectDTO)) {
                var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", "Item not found") };
                throw new FluentValidation.ValidationException("", errors);
            }
            Validate(dto);

            try {
                return await _repo.Update(dto);
            }
            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", ex.ToString()) };
                    throw new FluentValidation.ValidationException("", errors);
                }
                throw;
            }
            catch (Exception) {
                throw;
            }
        }

        public async Task<IEnumerable<ProjectDTO>> ReadEmployeeProjects(int employeeId) {
            return await _repo.ReadEmployeeProjects(employeeId);
        }

        public async Task<IEnumerable<ProjectDTO>> ReadProjects() {
            return await _repo.ReadProjects();
        }

        public async Task<IEnumerable<ProjectActivityDTO>> ReadProjectActivities() {
            return await _repo.ReadProjectActivities();
        }

        public async Task<IEnumerable<ProjectEmployeeActivityDTO>> ReadEmployeeProjectActivities(int employeeId) {
            return await _repo.ReadEmployeeProjectActivities(employeeId);
        }

        public void Dispose() {
        }

        public void Validate(ProjectDTO dto) {
            var validator = new ProjectValidator();
            validator.ValidateAndThrow(dto);
        }

        public class ProjectValidator : AbstractValidator<ProjectDTO>
        {
            public ProjectValidator() {
                RuleFor(x => x.ProjectName).NotNull();
                RuleFor(x => x.ProjectName).Length(0, 50).WithMessage("yo len too long");
            }
        }
    }
}
