﻿using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using FluentValidation;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DTO.Admin;
using PA.PAPA.Logic.Admin.Interfaces;

namespace PA.PAPA.Logic.Admin
{
    public class ActivityLogic : IActivityLogic
    {
        private readonly IActivityRepository _repo;

        public ActivityLogic(IActivityRepository ActivityRepository) {
            _repo = ActivityRepository;
        }

        public async Task<IList<ActivityDTO>> Read() {
            return await _repo.Read();
        }

        public async Task<ActivityDTO> Read(int id) {
            return await _repo.Read(id);
        }

        public async Task<ActivityDTO> Create(ActivityDTO dto) {
            Validate(dto);
            return await _repo.Create(dto);
        }

        public async Task<ActivityDTO> Update(ActivityDTO dto) {
            if (dto == null) {
                var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", "Item not set") };
                throw new FluentValidation.ValidationException("", errors);
            }

            var existing = await _repo.Read(dto.ActivityId);
            if (existing == default(ActivityDTO)) {
                var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", "Item not found") };
                throw new FluentValidation.ValidationException("", errors);
            }
            Validate(dto);

            try {
                return await _repo.Update(dto);
            }
            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", ex.ToString()) };
                    throw new FluentValidation.ValidationException("", errors);
                }
                throw;
            }
            catch (Exception) {
                throw;
            }
        }

        public void Dispose() {
        }

        public void Validate(ActivityDTO dto) {
            var validator = new ActivityValidator();
            validator.ValidateAndThrow(dto);
        }

        public class ActivityValidator : AbstractValidator<ActivityDTO>
        {
            public ActivityValidator() {
                RuleFor(x => x.Name).NotNull();
                RuleFor(x => x.Name).Length(0, 50).WithMessage("yo len too long");
                RuleFor(x => x.Code).Length(0, 50);
            }
        }
    }
}