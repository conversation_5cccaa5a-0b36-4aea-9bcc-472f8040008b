{"Files": [{"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html", "PackagePath": "staticwebassets\\beer-ocr-app.html"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.html", "PackagePath": "staticwebassets\\buttonTest.html"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.js", "PackagePath": "staticwebassets\\buttonTest.js"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\index.html", "PackagePath": "staticwebassets\\index.html"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.html", "PackagePath": "staticwebassets\\main.html"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.js", "PackagePath": "staticwebassets\\main.js"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.css", "PackagePath": "staticwebassets\\mainForm.css"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html", "PackagePath": "staticwebassets\\mainForm.html"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html.bak", "PackagePath": "staticwebassets\\mainForm.html.bak"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.js", "PackagePath": "staticwebassets\\mainForm.js"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.css", "PackagePath": "staticwebassets\\ocrtest.css"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.html", "PackagePath": "staticwebassets\\ocrtest.html"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.js", "PackagePath": "staticwebassets\\ocrtest.js"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simple-upload-test.html", "PackagePath": "staticwebassets\\simple-upload-test.html"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simpleTest.html", "PackagePath": "staticwebassets\\simpleTest.html"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\styles.css", "PackagePath": "staticwebassets\\styles.css"}, {"Id": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\upload-test.html", "PackagePath": "staticwebassets\\upload-test.html"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.NT.BIRAOCR.API.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.NT.BIRAOCR.API.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.build.NT.BIRAOCR.API.props", "PackagePath": "build\\NT.BIRAOCR.API.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildMultiTargeting.NT.BIRAOCR.API.props", "PackagePath": "buildMultiTargeting\\NT.BIRAOCR.API.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildTransitive.NT.BIRAOCR.API.props", "PackagePath": "buildTransitive\\NT.BIRAOCR.API.props"}], "ElementsToRemove": []}