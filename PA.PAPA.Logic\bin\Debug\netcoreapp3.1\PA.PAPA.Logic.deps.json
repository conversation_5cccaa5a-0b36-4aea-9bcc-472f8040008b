{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {"PA.PAPA.Logic/1.0.0": {"dependencies": {"FluentValidation.AspNetCore": "9.0.1", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "3.1.1", "PA.PAPA.DTO": "1.0.0", "PA.PAPA.DataAccess": "1.0.0"}, "runtime": {"PA.PAPA.Logic.dll": {}}}, "Dapper/2.0.35": {"dependencies": {"System.Reflection.Emit.Lightweight": "4.7.0"}, "runtime": {"lib/netstandard2.0/Dapper.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.35.21366"}}}, "FluentValidation/9.0.1": {"runtime": {"lib/netstandard2.0/FluentValidation.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.1.0"}}}, "FluentValidation.AspNetCore/9.0.1": {"dependencies": {"FluentValidation": "9.0.1", "FluentValidation.DependencyInjectionExtensions": "9.0.1"}, "runtime": {"lib/netcoreapp3.1/FluentValidation.AspNetCore.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.1.0"}}}, "FluentValidation.DependencyInjectionExtensions/9.0.1": {"dependencies": {"FluentValidation": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.6"}, "runtime": {"lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.1.0"}}}, "Microsoft.AspNetCore.JsonPatch/3.1.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.100.119.61510"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/3.1.1": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "3.1.1", "Newtonsoft.Json": "12.0.2", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.100.119.61510"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.6": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.620.31605"}}}, "Microsoft.Extensions.Options/3.1.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.6", "Microsoft.Extensions.Primitives": "3.1.6"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.620.31605"}}}, "Microsoft.Extensions.Primitives/3.1.6": {"runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.620.31605"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Newtonsoft.Json/12.0.2": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "12.0.2.23222"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "12.0.2"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "System.Data.SqlClient/4.8.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}}}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "PA.PAPA.DataAccess/1.0.0": {"dependencies": {"Dapper": "2.0.35", "Microsoft.Extensions.Options": "3.1.6", "PA.PAPA.DTO": "1.0.0", "System.Data.SqlClient": "4.8.1"}, "runtime": {"PA.PAPA.DataAccess.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "PA.PAPA.DTO/1.0.0": {"dependencies": {"FluentValidation.AspNetCore": "9.0.1"}, "runtime": {"PA.PAPA.DTO.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"PA.PAPA.Logic/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dapper/2.0.35": {"type": "package", "serviceable": true, "sha512": "sha512-/xAgd8BO8EDnJ0sURWEV8LptHHvTKxoYiT63YUF2U/yWE2VyUCqR2jcrtEyNngT9Kjzppecz95UKiBla3PnR7g==", "path": "dapper/2.0.35", "hashPath": "dapper.2.0.35.nupkg.sha512"}, "FluentValidation/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GeF+cPknUFFJ5FkCybmQShaXWwJowBuJozgns6In37wiP2zOWx+kdNPBAN9BlDyhcORdxtXeGnkLZ+OFT85onQ==", "path": "fluentvalidation/9.0.1", "hashPath": "fluentvalidation.9.0.1.nupkg.sha512"}, "FluentValidation.AspNetCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7DlvStNVZwSAbLC5GyT3nGa8K11hb9R+W6BgnYnSjvnPFGWKpyANwuGTl8HShOInjXJqIJykqHo1UcdemTq/7g==", "path": "fluentvalidation.aspnetcore/9.0.1", "hashPath": "fluentvalidation.aspnetcore.9.0.1.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-h71E7XXjFJZsIs6s7AJz2uF4g2BTmEJk3mTKviVRtXuAUF9yV3B7glZjIpEncV6i4u7caqFixKeVpSBB3jVL9w==", "path": "fluentvalidation.dependencyinjectionextensions/9.0.1", "hashPath": "fluentvalidation.dependencyinjectionextensions.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Y2hwnbYzA8nmRH3+eTXtG+HP7rkMSLcqcLh5vfoN/J3zcmYb7vMtRauSDT9GO85JGwk+blNiCDXEou8Dj2TR4g==", "path": "microsoft.aspnetcore.jsonpatch/3.1.1", "hashPath": "microsoft.aspnetcore.jsonpatch.3.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-t8vDVyivm/rnWvzvmVKGJUf7w8Mz1C4T3qnPAm0WyEU6LRt4WdLu4k1g8jVQ4qZTR7NDzv2DR0F2VSjZvkQdtQ==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/3.1.1", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.3.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-JVlypPjlPqOEyAhET6pA7TqrK+Ya8TrYe2vvqsp62TYKS87oNuReoS7XpDFW0rrOsQo66G1yYf3K0E+lJHFHuw==", "path": "microsoft.extensions.dependencyinjection.abstractions/3.1.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.3.1.6.nupkg.sha512"}, "Microsoft.Extensions.Options/3.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-6nBkdL9UxCfA8eDuFBZVYAYpJhux0a6R7y58KcYnqKEYF84X9cK1uJQ9a+VWEWGXujMa4fnCGoVdKNYhANqbZg==", "path": "microsoft.extensions.options/3.1.6", "hashPath": "microsoft.extensions.options.3.1.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/3.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-X+h3tO383iMbtFxYp6Al4pgBzbfQKJY8/gd4U7+sZy90XeSfnw5SwHyCiRrAePG6kee4zCGsvQHPCqaTZ876hw==", "path": "microsoft.extensions.primitives/3.1.6", "hashPath": "microsoft.extensions.primitives.3.1.6.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Newtonsoft.Json/12.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-rTK0s2EKlfHsQsH6Yx2smvcTCeyoDNgCW7FEYyV01drPlh2T243PR2DiDXqtC5N4GDm4Ma/lkxfW5a/4793vbA==", "path": "newtonsoft.json/12.0.2", "hashPath": "newtonsoft.json.12.0.2.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-HKLykcv6eZLbLnSMnlQ6Os4+UAmFE+AgYm92CTvJYeTOBtOYusX3qu8OoGhFrnKZax91UcLcDo5vPrqvJUTSNQ==", "path": "system.data.sqlclient/4.8.1", "hashPath": "system.data.sqlclient.4.8.1.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "PA.PAPA.DataAccess/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "PA.PAPA.DTO/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}