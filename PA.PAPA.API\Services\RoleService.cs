﻿using System.Collections.Generic;
using BooksApi.Models;
using MongoDB.Driver;
using PA.PAPA.API.Models;

namespace PA.PAPA.API.Services
{
    public class RoleService
    {
        private readonly IMongoCollection<Role> _role;

        public RoleService(IRoleDatabaseSettings settings)
        {
            var client = new MongoClient(settings.ConnectionString);
            var database = client.GetDatabase(settings.DatabaseName);

            _role = database.GetCollection<Role>(settings.RoleCollectionName);
        }

        public List<Role> Get()
        {
            return _role.Find(role => true).ToList();
        }

        //public List<Role> GetByEmployeeNumber(string employeeNumber)
        //{
        //    return _role.Find(role => role.EmployeeNumber.ToUpper() == employeeNumber.ToUpper()).ToList();
        //}

        public Role Get(string id)
        {
            return _role.Find(role => role.Id == id).FirstOrDefault();
        }

        //public List<Role> Get(int userId)
        //{
        //    return _role.Find(role => role.UserId == userId).ToList();
        //}

        public Role Create(Role role)
        {
            _role.InsertOne(role);
            return role;
        }

        public void Update(string id, Role roleIn)
        {
            _role.ReplaceOne(role => role.Id == id, roleIn);
        }

        public void Remove(Role roleIn)
        {
            _role.DeleteOne(role => role.Id == roleIn.Id);
        }

        public void Remove(string id)
        {
            _role.DeleteOne(role => role.Id == id);
        }
    }
}