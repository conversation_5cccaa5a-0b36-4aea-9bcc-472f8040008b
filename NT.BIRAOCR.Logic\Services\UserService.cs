/*
 * © 2025 <PERSON> | SmartIT. All Rights Reserved.
 * https://johnkocer.github.io
 * Version: 1.0.0 Date: 05-21-2025
 */

using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using NT.BIRAOCR.DataAccess;
using NT.BIRAOCR.DTO;
using NT.BIRAOCR.Logic.Interfaces;

namespace NT.BIRAOCR.Logic.Services
{
    public class UserService : IUserService
    {
        private readonly IConnectionFactory _connectionFactory;
        private readonly IConfiguration _configuration;

        public UserService(IConnectionFactory connectionFactory, IConfiguration configuration)
        {
            _connectionFactory = connectionFactory;
            _configuration = configuration;
        }

        public async Task<AuthResponseDto> RegisterAsync(UserRegistrationDto registration)
        {
            // In a real implementation, this would validate the user data and store it in the database
            // For this demo, we'll just create a mock user and return a token
            
            // Create a mock user
            var user = new UserDto
            {
                UserId = 1,
                Username = registration.Username,
                Email = registration.Email,
                IsActive = true
            };

            // Generate a token
            var token = GenerateJwtToken(user);
            
            return new AuthResponseDto
            {
                Token = token,
                Expiration = DateTime.UtcNow.AddDays(7),
                User = user
            };
        }

        public async Task<AuthResponseDto> LoginAsync(UserLoginDto login)
        {
            // In a real implementation, this would validate the credentials against the database
            // For this demo, we'll just create a mock user and return a token
            
            // Create a mock user
            var user = new UserDto
            {
                UserId = 1,
                Username = login.Username,
                Email = $"{login.Username}@example.com",
                IsActive = true
            };

            // Generate a token
            var token = GenerateJwtToken(user);
            
            return new AuthResponseDto
            {
                Token = token,
                Expiration = DateTime.UtcNow.AddDays(7),
                User = user
            };
        }

        public async Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Key"] ?? "defaultSecretKeyForDevelopmentOnly12345678901234");
                
                tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                return true;
            }
            catch
            {
                return false;
            }
        }

        private string GenerateJwtToken(UserDto user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Key"] ?? "defaultSecretKeyForDevelopmentOnly12345678901234");
            
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.NameIdentifier, user.UserId.ToString()),
                    new Claim(ClaimTypes.Name, user.Username),
                    new Claim(ClaimTypes.Email, user.Email)
                }),
                Expires = DateTime.UtcNow.AddDays(7),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
    }
}
