﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.DataAccess.Repositories.Admin.Interfaces
{
    public interface IActivityRepository : IDisposable
    {
        Task<IList<ActivityDTO>> Read();
        Task<ActivityDTO> Read(int id);
        Task<ActivityDTO> Create(ActivityDTO dto);
        Task<ActivityDTO> Update(ActivityDTO dto);
    }
}