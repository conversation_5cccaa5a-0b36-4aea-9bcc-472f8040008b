﻿using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.Text;

namespace PA.PAPA.DTO.Core
{
    public class ProjectDTO
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; }
        public int ProjectTypeId { get; set; }
        public int OwnerEmployeeId { get; set; }
        public int ProjectStatusId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int CompanySegmentId { get; set; }
        public int SiteSegmentId { get; set; }
        public int FunctionSegmentId { get; set; }
        public int ApplicationId { get; set; }
        public string AFE { get; set; }
        public String AFEAmount { get; set; }
        public bool CapitalLaborProject { get; set; }
        public bool ExternalSpend { get; set; }
        public bool Budgeted { get; set; }
        public int CreatedById { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedById { get; set; }
        public DateTime UpdatedDate { get; set; }
        public bool IsActive { get; set; }
                
    }
}
