C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\appsettings.Development.json
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\appsettings.json
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.API.exe
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.API.deps.json
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.API.runtimeconfig.json
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.API.runtimeconfig.dev.json
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.API.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.API.pdb
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Dapper.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\DnsClient.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\FluentValidation.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\FluentValidation.AspNetCore.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\FluentValidation.DependencyInjectionExtensions.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.JsonPatch.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.DotNet.PlatformAbstractions.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyModel.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Logging.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Tokens.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Microsoft.OpenApi.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\MongoDB.Bson.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\MongoDB.Driver.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\MongoDB.Driver.Core.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\MongoDB.Libmongocrypt.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Newtonsoft.Json.Bson.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.AspNetCore.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Enrichers.Environment.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Extensions.Hosting.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Extensions.Logging.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Formatting.Compact.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Settings.Configuration.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Sinks.Async.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Sinks.Console.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Sinks.Debug.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Sinks.File.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Sinks.PeriodicBatching.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Sinks.RollingFile.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Serilog.Sinks.Seq.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\SharpCompress.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\SmartIT.DebugHelper.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.Swagger.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\System.Data.SqlClient.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\System.IdentityModel.Tokens.Jwt.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\runtimes\win\native\libzstd.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\runtimes\win\native\snappy32.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\runtimes\win\native\snappy64.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\runtimes\win-arm64\native\sni.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\sni.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\sni.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.DataAccess.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.DTO.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.Logic.dll
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.DataAccess.pdb
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.DTO.pdb
C:\Net9\PAPA_API\PA.PAPA.API\bin\Debug\netcoreapp3.1\PA.PAPA.Logic.pdb
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.csproj.AssemblyReference.cache
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.GeneratedMSBuildEditorConfig.editorconfig
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.AssemblyInfoInputs.cache
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.AssemblyInfo.cs
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.csproj.CoreCompileInputs.cache
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.MvcApplicationPartsAssemblyInfo.cs
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.MvcApplicationPartsAssemblyInfo.cache
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.sourcelink.json
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.RazorTargetAssemblyInfo.cache
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\staticwebassets\PA.PAPA.API.StaticWebAssets.Manifest.cache
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\staticwebassets\PA.PAPA.API.StaticWebAssets.Pack.cache
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.PA.PAPA.API.Microsoft.AspNetCore.StaticWebAssets.props
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.build.PA.PAPA.API.props
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildMultiTargeting.PA.PAPA.API.props
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildTransitive.PA.PAPA.API.props
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA..9A76BB0A.Up2Date
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.dll
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.pdb
C:\Net9\PAPA_API\PA.PAPA.API\obj\Debug\netcoreapp3.1\PA.PAPA.API.genruntimeconfig.cache
