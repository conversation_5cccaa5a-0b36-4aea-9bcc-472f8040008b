﻿using System.Collections.Generic;
using BooksApi.Models;
using MongoDB.Driver;
using PA.PAPA.API.Models;

namespace PA.PAPA.API.Services
{
    public class UserClaimService
    {
        private readonly IMongoCollection<UserClaim> _userClaims;

        public UserClaimService(IUserClaimDatabaseSettings settings)
        {
            var client = new MongoClient(settings.ConnectionString);
            var database = client.GetDatabase(settings.DatabaseName);

            _userClaims = database.GetCollection<UserClaim>(settings.UserClaimsCollectionName);
        }

        public List<UserClaim> Get()
        {
            return _userClaims.Find(userClaim => true).ToList();
        }

        public List<UserClaim> GetByEmployeeNumber(string employeeNumber)
        {
            return _userClaims.Find(userClaim => userClaim.EmployeeNumber.ToUpper() == employeeNumber.ToUpper()).ToList();
        }

        public UserClaim Get(string id)
        {
            return _userClaims.Find(userClaim => userClaim.Id == id).FirstOrDefault();
        }

        public List<UserClaim> Get(int userId)
        {
            return _userClaims.Find(userClaim => userClaim.UserId == userId).ToList();
        }

        public UserClaim Create(UserClaim userClaim)
        {
            _userClaims.InsertOne(userClaim);
            return userClaim;
        }

        public void Update(string id, UserClaim userClaimIn)
        {
            _userClaims.ReplaceOne(userClaim => userClaim.Id == id, userClaimIn);
        }

        public void Remove(UserClaim userClaimIn)
        {
            _userClaims.DeleteOne(userClaim => userClaim.Id == userClaimIn.Id);
        }

        public void Remove(string id)
        {
            _userClaims.DeleteOne(userClaim => userClaim.Id == id);
        }
    }
}