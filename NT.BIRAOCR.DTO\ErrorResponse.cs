/*
 * © 2025 <PERSON> | SmartIT. All Rights Reserved.
 * https://johnkocer.github.io
 * Version: 1.0.0 Date: 05-21-2025
 */

using System.Collections.Generic;

namespace NT.BIRAOCR.DTO
{
    public class ErrorResponse
    {
        public required string Message { get; set; }
        public required string ErrorCode { get; set; }
        public List<PropertyValidationError> ValidationErrors { get; set; } = new List<PropertyValidationError>();
    }
}
