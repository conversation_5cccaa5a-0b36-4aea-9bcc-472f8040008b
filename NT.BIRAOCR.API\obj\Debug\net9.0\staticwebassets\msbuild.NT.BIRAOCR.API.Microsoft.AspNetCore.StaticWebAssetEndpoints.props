﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/beer-ocr-app.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\beer-ocr-app.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1173"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:38:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/beer-ocr-app.lq6hyijkef.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\beer-ocr-app.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lq6hyijkef"},{"Name":"integrity","Value":"sha256-XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/beer-ocr-app.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1173"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:38:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/buttonTest.5ipweew5fc.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\buttonTest.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5ipweew5fc"},{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/buttonTest.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/buttonTest.5ipweew5fc.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\buttonTest.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5ipweew5fc"},{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/buttonTest.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/buttonTest.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\buttonTest.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/buttonTest.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\buttonTest.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/index.4a7d5925mm.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4a7d5925mm"},{"Name":"integrity","Value":"sha256-5FAzzLLFg0fhmMwgw9ZWM\u002B1KeTn6\u002BtzvqdS/qog\u002Br8Q="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/index.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"8810"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u00225FAzzLLFg0fhmMwgw9ZWM\u002B1KeTn6\u002BtzvqdS/qog\u002Br8Q=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 00:49:42 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/index.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5FAzzLLFg0fhmMwgw9ZWM\u002B1KeTn6\u002BtzvqdS/qog\u002Br8Q="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"8810"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u00225FAzzLLFg0fhmMwgw9ZWM\u002B1KeTn6\u002BtzvqdS/qog\u002Br8Q=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 00:49:42 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/main.13ups3th7z.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\main.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"13ups3th7z"},{"Name":"integrity","Value":"sha256-cwNmIDCDRl\u002B\u002BnWvg5dK/zqAKtiL3\u002BIrQD2prGBHKS8Y="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/main.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4265"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022cwNmIDCDRl\u002B\u002BnWvg5dK/zqAKtiL3\u002BIrQD2prGBHKS8Y=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 00:53:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/main.bx607eef1i.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\main.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bx607eef1i"},{"Name":"integrity","Value":"sha256-8d3F/ZWp3VK5gELlIDcbcHZhkaSuH\u002Bj5Gm/\u002BeBRCazk="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/main.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3160"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u00228d3F/ZWp3VK5gELlIDcbcHZhkaSuH\u002Bj5Gm/\u002BeBRCazk=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 01:28:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/main.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\main.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-8d3F/ZWp3VK5gELlIDcbcHZhkaSuH\u002Bj5Gm/\u002BeBRCazk="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"3160"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u00228d3F/ZWp3VK5gELlIDcbcHZhkaSuH\u002Bj5Gm/\u002BeBRCazk=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 01:28:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/main.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\main.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-cwNmIDCDRl\u002B\u002BnWvg5dK/zqAKtiL3\u002BIrQD2prGBHKS8Y="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4265"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022cwNmIDCDRl\u002B\u002BnWvg5dK/zqAKtiL3\u002BIrQD2prGBHKS8Y=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 00:53:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/mainForm.5ipweew5fc.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\mainForm.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5ipweew5fc"},{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/mainForm.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/mainForm.5ipweew5fc.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\mainForm.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5ipweew5fc"},{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/mainForm.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/mainForm.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\mainForm.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/mainForm.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\mainForm.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5897"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:38:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/mainForm.html.bak">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\mainForm.html.bak'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Ze6vaspCi2PrUT87dEqck\u002BymuqMKLx1xLIely4az75w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5887"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022Ze6vaspCi2PrUT87dEqck\u002BymuqMKLx1xLIely4az75w=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:30:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/mainForm.html.z6m5epkocb.bak">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\mainForm.html.bak'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z6m5epkocb"},{"Name":"integrity","Value":"sha256-Ze6vaspCi2PrUT87dEqck\u002BymuqMKLx1xLIely4az75w="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/mainForm.html.bak"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5887"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u0022Ze6vaspCi2PrUT87dEqck\u002BymuqMKLx1xLIely4az75w=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:30:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/mainForm.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\mainForm.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/mainForm.lt4h0pu2ew.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\mainForm.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lt4h0pu2ew"},{"Name":"integrity","Value":"sha256-qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/mainForm.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5897"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:38:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/ocrtest.7swnv70qx5.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\ocrtest.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7swnv70qx5"},{"Name":"integrity","Value":"sha256-SX9S8WTXYwPXKxHfCb3FYi2s\u002BQmyJgelGz6WicdrvM0="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/ocrtest.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"16388"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022SX9S8WTXYwPXKxHfCb3FYi2s\u002BQmyJgelGz6WicdrvM0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:30:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/ocrtest.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\ocrtest.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"7773"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u002250yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:03:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/ocrtest.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\ocrtest.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5887"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:38:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/ocrtest.jp5omh6saj.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\ocrtest.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"jp5omh6saj"},{"Name":"integrity","Value":"sha256-50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/ocrtest.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"7773"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u002250yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:03:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/ocrtest.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\ocrtest.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-SX9S8WTXYwPXKxHfCb3FYi2s\u002BQmyJgelGz6WicdrvM0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"16388"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022SX9S8WTXYwPXKxHfCb3FYi2s\u002BQmyJgelGz6WicdrvM0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:30:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/ocrtest.zh1ztznphx.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\ocrtest.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"zh1ztznphx"},{"Name":"integrity","Value":"sha256-qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/ocrtest.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5887"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:38:18 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/simple-upload-test.20rbgnipeh.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\simple-upload-test.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"20rbgnipeh"},{"Name":"integrity","Value":"sha256-concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/\u002BclnDkPdT0="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/simple-upload-test.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"2877"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/\u002BclnDkPdT0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:30:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/simple-upload-test.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\simple-upload-test.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/\u002BclnDkPdT0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"2877"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/\u002BclnDkPdT0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 23 May 2025 14:30:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/simpleTest.5ipweew5fc.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\simpleTest.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5ipweew5fc"},{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/simpleTest.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/simpleTest.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\simpleTest.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/styles.7ppuzqynnz.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\styles.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7ppuzqynnz"},{"Name":"integrity","Value":"sha256-m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/styles.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1599"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 00:49:42 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/styles.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\styles.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1599"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=\u0022"},{"Name":"Last-Modified","Value":"Tue, 20 May 2025 00:49:42 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/upload-test.5ipweew5fc.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\upload-test.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5ipweew5fc"},{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="},{"Name":"label","Value":"_content/NT.BIRAOCR.API/upload-test.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/NT.BIRAOCR.API/upload-test.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\upload-test.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Mon, 26 May 2025 19:55:57 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>