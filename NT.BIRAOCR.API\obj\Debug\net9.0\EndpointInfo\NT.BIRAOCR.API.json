{"openapi": "3.0.4", "info": {"title": "NT.BIRAOCR.API", "version": "1.0"}, "paths": {"/api/Analysis/submit": {"post": {"tags": ["Analysis"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalysisRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AnalysisRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AnalysisRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Analysis/upload-image": {"post": {"tags": ["Analysis"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegistrationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRegistrationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserRegistrationDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLoginDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/validate-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/": {"get": {"tags": ["NT.BIRAOCR.API"], "responses": {"200": {"description": "OK"}}}}, "/api/OCR/extract": {"post": {"tags": ["OCR"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"image": {"type": "string", "format": "binary"}}}, "encoding": {"image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/OCR/extractTest": {"post": {"tags": ["OCR"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"image": {"type": "string", "format": "binary"}, "prompt": {"type": "string"}, "userText": {"type": "string"}}}, "encoding": {"image": {"style": "form"}, "prompt": {"style": "form"}, "userText": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AnalysisRequestDto": {"required": ["customerName", "customerNumber", "imagePath", "location", "menuItem"], "type": "object", "properties": {"customerName": {"type": "string", "nullable": true}, "customerNumber": {"type": "string", "nullable": true}, "requestDate": {"type": "string", "format": "date-time"}, "imagePath": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "menuItem": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserLoginDto": {"required": ["password", "username"], "type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserRegistrationDto": {"required": ["email", "password", "username"], "type": "object", "properties": {"username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}