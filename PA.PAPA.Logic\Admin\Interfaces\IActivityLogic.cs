﻿using System;
using System.Collections.Generic;
using System.Text;
using PA.PAPA.DTO.Admin;
using System.Threading.Tasks;

namespace PA.PAPA.Logic.Admin.Interfaces
{
    public interface IActivityLogic : IDisposable
    {
        Task<IList<ActivityDTO>> Read();
        Task<ActivityDTO> Read(int id);
        Task<ActivityDTO> Create(ActivityDTO dto);
        Task<ActivityDTO> Update(ActivityDTO dto);
    }
}
