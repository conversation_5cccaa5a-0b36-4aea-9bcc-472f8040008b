﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PA.PAPA.DTO.Core;

namespace PA.PAPA.DataAccess.Repositories.Core.Interfaces
{
    public interface ITimesheetRepository
    {
        Task<IEnumerable<TimesheetDTO>> Read(DateTime startDate, int employeeId);
        Task<TimesheetDTO> Read(int id);
        //Task<IEnumerable<TimesheetDTO>> Save(IEnumerable<TimesheetDTO> dtos);
        Task<int> Create(TimesheetDTO dto);
        Task<int> Update(TimesheetDTO dto);
        Task<int> Delete(int id);

    }
}
