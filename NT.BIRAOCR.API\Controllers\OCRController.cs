/*
 * © 2025 <PERSON> | SmartIT. All Rights Reserved.
 * https://johnkocer.github.io
 * Version: 1.0.0 Date: 05-21-2025
 */

using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.Net.Http.Headers;
using System.Diagnostics;

namespace NT.BIRAOCR.API.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  public class OCRController : ControllerBase
  {
    private readonly ILogger<OCRController> _logger;
    private readonly IConfiguration _configuration;
    private readonly IWebHostEnvironment _environment;
    private readonly HttpClient _httpClient;

    public OCRController(
        ILogger<OCRController> logger,
        IConfiguration configuration,
        IWebHostEnvironment environment)
    {
      _logger = logger;
      _configuration = configuration;
      _environment = environment;

      // Create HTTP client manually for simplicity
      _httpClient = new HttpClient();
      _httpClient.BaseAddress = new Uri("https://api.openai.com/");
      _httpClient.Timeout = TimeSpan.FromSeconds(120);

      // Configure OpenAI API key from settings
      string apiKey = _configuration["OpenAI:ApiKey"] ?? "sk-your-openai-api-key-here";
      _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
      _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
    }

    [HttpPost("extract")]
    public async Task<IActionResult> ExtractText(IFormFile image)
    {
      try
      {
        if (image == null || image.Length == 0)
        {
          return BadRequest("No image file provided");
        }

        // Validate file type
        string[] permittedExtensions = { ".jpg", ".jpeg", ".png", ".bmp" };
        string extension = Path.GetExtension(image.FileName).ToLowerInvariant();

        if (string.IsNullOrEmpty(extension) || !permittedExtensions.Contains(extension))
        {
          return BadRequest("Invalid file type. Please upload a JPG, PNG, or BMP image.");
        }

        // Save the uploaded image to a temporary file
        var uploadsFolder = Path.Combine(_environment.ContentRootPath, "uploads");
        Directory.CreateDirectory(uploadsFolder); // Ensure folder exists

        string uniqueFileName = Guid.NewGuid().ToString() + extension;
        string filePath = Path.Combine(uploadsFolder, uniqueFileName);

        using (var stream = new FileStream(filePath, FileMode.Create))
        {
          await image.CopyToAsync(stream);
        }

        // Extract text from the image
        string extractedText = await ExtractTextFromImage(filePath);

        // Clean up the temporary file
        try
        {
          System.IO.File.Delete(filePath);
        }
        catch (Exception ex)
        {
          _logger.LogWarning($"Could not delete temporary file {filePath}: {ex.Message}");
        }

        return Ok(new { success = true, result = extractedText });
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error processing OCR request");
        return StatusCode(500, new { success = false, message = $"Error processing OCR request: {ex.Message}" });
      }
    }    [HttpPost("extractTest")]
    public async Task<IActionResult> ExtractTextTest(IFormFile image, [FromForm] string? prompt = null, [FromForm] string? userText = null)
    {
      try
      {
        if (image == null || image.Length == 0)
        {
          return BadRequest("No image file provided");
        }

        // Validate file type
        string[] permittedExtensions = { ".jpg", ".jpeg", ".png", ".bmp" };
        string extension = Path.GetExtension(image.FileName).ToLowerInvariant();

        if (string.IsNullOrEmpty(extension) || !permittedExtensions.Contains(extension))
        {
          return BadRequest("Invalid file type. Please upload a JPG, PNG, or BMP image.");
        }

        // Save the uploaded image to a temporary file
        var uploadsFolder = Path.Combine(_environment.ContentRootPath, "uploads");
        Directory.CreateDirectory(uploadsFolder); // Ensure folder exists

        string uniqueFileName = Guid.NewGuid().ToString() + extension;
        string filePath = Path.Combine(uploadsFolder, uniqueFileName);

        using (var stream = new FileStream(filePath, FileMode.Create))
        {
          await image.CopyToAsync(stream);
        }        
        // Extract text from the image
        string extractedText = await ExtractTextFromImageTest(filePath, prompt, userText);

        // Clean up the temporary file
        try
        {
          System.IO.File.Delete(filePath);
        }
        catch (Exception ex)
        {
          _logger.LogWarning($"Could not delete temporary file {filePath}: {ex.Message}");
        }

        return Ok(new { success = true, result = extractedText });
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error processing OCR request");
        return StatusCode(500, new { success = false, message = $"Error processing OCR request: {ex.Message}" });
      }
    }

    private async Task<string> ExtractTextFromImage(string imagePathOrUrl)
    {
      try
      {
        byte[] imageBytes;
        if (Uri.IsWellFormedUriString(imagePathOrUrl, UriKind.Absolute) &&
            (imagePathOrUrl.StartsWith("http://") || imagePathOrUrl.StartsWith("https://")))
        {
          imageBytes = await _httpClient.GetByteArrayAsync(imagePathOrUrl);
        }
        else
        {
          // Fix for CS0119: Fully qualify the System.IO.File class to avoid ambiguity with ControllerBase.File method.
          imageBytes = await System.IO.File.ReadAllBytesAsync(imagePathOrUrl);
        }

        var base64 = Convert.ToBase64String(imageBytes);
        var imageDataUri = $"data:image/jpeg;base64,{base64}"; // or image/png

        var requestBody = new
        {
          model = "gpt-4o",
          messages = new object[]
          {
              new
              {
                  role = "system",
                  //content = @"You are an assistant that extracts drink names and prices from German drink menus. Ignore sizes like '0,3L' or 'ml'. Format each line as: [Drink Name] - [Price]€ (e.g., Becks - 4.50€)."
                  content = $"You are an assistant that formats **German drink menus** extracted via OCR. Your task is to:  \r\n1. **Extract drink names and their prices** from the provided text.  \r\n2. **Ignore volume information** (e.g., \"cl\", \"ml\", \"L\", \"0,3L\").  \r\n3. **Standardize prices**:  \r\n   - Convert `X,XX€` or `je X,XX` to `X.XX` (e.g., \"5,90€\" → \"5.90\").  \r\n   - If no currency symbol is present, assume prices are in **Euros** (e.g., \"3,50\" → \"3.50\").  \r\n4. **Output format**:  \r\n   - Each line should contain: `[Drink Name] - [Price]€`  \r\n   - Skip incomplete or fragmented entries (e.g., missing prices or names).  \r\n\r\n**Example Input:**  "
              },

              new
              {
                  role = "user",
                  content = new object[]
                  {
                      new { type = "text",
                        //text = "Extract drink menu items from this image" 
                                                //text = $"Analyze the following German drink menu and extract all drink names along with their prices formatted as \"je X,XX\" (e.g., \"je 6,90\"). Return the results in a clean list with the drink name followed by its price in USD format (e.g., \"6.90\"). Ignore any incomplete or fragmented entries.  \r\n\r\nGiven menu:   :\n\n{extractedText}"
                                                text = $"Analyze the following German drink menu and extract all drink names along with their prices formatted as \"je X,XX\" (e.g., \"je 6,90\"). Return the results in a clean list with the drink name followed by its price in USD format (e.g., \"6.90\"). Ignore any incomplete or fragmented entries.  \r\n\r\nGiven menu:   :\n\n"

                      },
                      new { type = "image_url", image_url = new { url = imageDataUri } }
                  }
              }
          },
          max_tokens = 200,
          temperature = 0.2
        };

        var res = await _httpClient.PostAsJsonAsync("https://api.openai.com/v1/chat/completions", requestBody);
        if (!res.IsSuccessStatusCode)
          throw new HttpRequestException($"GPT Vision error {res.StatusCode}");

        var json = await res.Content.ReadFromJsonAsync<JsonDocument>()
            ?? throw new Exception("Invalid GPT response");

        var output = json.RootElement
            .GetProperty("choices")[0]
            .GetProperty("message")
            .GetProperty("content")
            .GetString() ?? "";

        _logger.LogInformation("Vision GPT result: {Result}", output);
        return output;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Vision OCR failed");
        return "";
      }
    }
    private async Task<string> ExtractTextFromImageTest(string imagePathOrUrl, string? customPrompt = default, string? userText = default)
    {
      try
      {
        byte[] imageBytes;
        if (Uri.IsWellFormedUriString(imagePathOrUrl, UriKind.Absolute) &&
            (imagePathOrUrl.StartsWith("http://") || imagePathOrUrl.StartsWith("https://")))
        {
          imageBytes = await _httpClient.GetByteArrayAsync(imagePathOrUrl);
        }
        else
        {
          // Fix for CS0119: Fully qualify the System.IO.File class to avoid ambiguity with ControllerBase.File method.
          imageBytes = await System.IO.File.ReadAllBytesAsync(imagePathOrUrl);
        }

        var base64 = Convert.ToBase64String(imageBytes);
        var imageDataUri = $"data:image/jpeg;base64,{base64}"; // or image/png

        var requestBody = new
        {
          model = "gpt-4o",
          messages = new object[]
          {
              new
              {
                  role = "system",
                  content = customPrompt ?? $"You are an assistant that formats **German drink menus** extracted via OCR. Your task is to:  \r\n1. **Extract drink names and their prices** from the provided text.  \r\n2. **Ignore volume information** (e.g., \"cl\", \"ml\", \"L\", \"0,3L\").  \r\n3. **Standardize prices**:  \r\n   - Convert `X,XX€` or `je X,XX` to `X.XX` (e.g., \"5,90€\" → \"5.90\").  \r\n   - If no currency symbol is present, assume prices are in **Euros** (e.g., \"3,50\" → \"3.50\").  \r\n4. **Output format**:  \r\n   - Each line should contain: `[Drink Name] - [Price]€`  \r\n   - Skip incomplete or fragmented entries (e.g., missing prices or names).  \r\n\r\n**Example Input:**  "
              },

              new
              {
                  role = "user",
                  content = new object[]
                  {
                      new { type = "text", text = userText ?? "Analyze the following German drink menu image." },
                      new { type = "image_url", image_url = new { url = imageDataUri } }
                  }
              }
          },
          max_tokens = 200,
          temperature = 0.2
        };

        var res = await _httpClient.PostAsJsonAsync("https://api.openai.com/v1/chat/completions", requestBody);
        if (!res.IsSuccessStatusCode)
          throw new HttpRequestException($"GPT Vision error {res.StatusCode}");

        var json = await res.Content.ReadFromJsonAsync<JsonDocument>()
            ?? throw new Exception("Invalid GPT response");

        var output = json.RootElement
            .GetProperty("choices")[0]
            .GetProperty("message")
            .GetProperty("content")
            .GetString() ?? "";

        _logger.LogInformation("Vision GPT result: {Result}", output);
        return output;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Vision OCR failed");
        return "";
      }
    }
  }
}
