{"Version": 1, "WorkspaceRootPath": "M:\\aOzan\\int\\MenuScan\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|m:\\aozan\\int\\menuscan\\nt.biraocr.api\\wwwroot\\beer-ocr-app.html||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|solutionrelative:nt.biraocr.api\\wwwroot\\beer-ocr-app.html||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|m:\\aozan\\int\\menuscan\\nt.biraocr.api\\wwwroot\\mainform.html||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|solutionrelative:nt.biraocr.api\\wwwroot\\mainform.html||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|m:\\aozan\\int\\menuscan\\nt.biraocr.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|solutionrelative:nt.biraocr.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{070FFFDC-3060-4BC2-AF85-43D32C5A4452}|NT.BIRAOCR.Logic\\NT.BIRAOCR.Logic.csproj|m:\\aozan\\int\\menuscan\\nt.biraocr.logic\\services\\analysisservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{070FFFDC-3060-4BC2-AF85-43D32C5A4452}|NT.BIRAOCR.Logic\\NT.BIRAOCR.Logic.csproj|solutionrelative:nt.biraocr.logic\\services\\analysisservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{070FFFDC-3060-4BC2-AF85-43D32C5A4452}|NT.BIRAOCR.Logic\\NT.BIRAOCR.Logic.csproj|m:\\aozan\\int\\menuscan\\nt.biraocr.logic\\interfaces\\iblobstorageservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{070FFFDC-3060-4BC2-AF85-43D32C5A4452}|NT.BIRAOCR.Logic\\NT.BIRAOCR.Logic.csproj|solutionrelative:nt.biraocr.logic\\interfaces\\iblobstorageservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|m:\\aozan\\int\\menuscan\\nt.biraocr.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|solutionrelative:nt.biraocr.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|m:\\aozan\\int\\menuscan\\nt.biraocr.api\\controllers\\ocrcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|solutionrelative:nt.biraocr.api\\controllers\\ocrcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|m:\\aozan\\int\\menuscan\\nt.biraocr.api\\controllers\\analysiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|solutionrelative:nt.biraocr.api\\controllers\\analysiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|m:\\aozan\\int\\menuscan\\nt.biraocr.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5338B300-0E53-4358-B358-429F276F65FE}|NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj|solutionrelative:nt.biraocr.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "beer-ocr-app.html", "DocumentMoniker": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html", "RelativeDocumentMoniker": "NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html", "ToolTip": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html", "RelativeToolTip": "NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html", "ViewState": "AgIAABIAAAAAAAAAAAAAACoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001512|", "WhenOpened": "2025-05-23T14:38:27.361Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "mainForm.html", "DocumentMoniker": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html", "RelativeDocumentMoniker": "NT.BIRAOCR.API\\wwwroot\\mainForm.html", "ToolTip": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html", "RelativeToolTip": "NT.BIRAOCR.API\\wwwroot\\mainForm.html", "ViewState": "AgIAAGAAAAAAAAAAAAAAAHMAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001512|", "WhenOpened": "2025-05-23T13:50:12.107Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "AnalysisService.cs", "DocumentMoniker": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.Logic\\Services\\AnalysisService.cs", "RelativeDocumentMoniker": "NT.BIRAOCR.Logic\\Services\\AnalysisService.cs", "ToolTip": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.Logic\\Services\\AnalysisService.cs", "RelativeToolTip": "NT.BIRAOCR.Logic\\Services\\AnalysisService.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAQwCQAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T01:18:51.988Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "IBlobStorageService.cs", "DocumentMoniker": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.Logic\\Interfaces\\IBlobStorageService.cs", "RelativeDocumentMoniker": "NT.BIRAOCR.Logic\\Interfaces\\IBlobStorageService.cs", "ToolTip": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.Logic\\Interfaces\\IBlobStorageService.cs", "RelativeToolTip": "NT.BIRAOCR.Logic\\Interfaces\\IBlobStorageService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T01:17:44.565Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "appsettings.json", "DocumentMoniker": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\appsettings.json", "RelativeDocumentMoniker": "NT.BIRAOCR.API\\appsettings.json", "ToolTip": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\appsettings.json", "RelativeToolTip": "NT.BIRAOCR.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-23T01:15:31.647Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "AnalysisController.cs", "DocumentMoniker": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\Controllers\\AnalysisController.cs", "RelativeDocumentMoniker": "NT.BIRAOCR.API\\Controllers\\AnalysisController.cs", "ToolTip": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\Controllers\\AnalysisController.cs", "RelativeToolTip": "NT.BIRAOCR.API\\Controllers\\AnalysisController.cs", "ViewState": "AgIAACUAAAAAAAAAAAAAADwAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T00:44:17.686Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\Program.cs", "RelativeDocumentMoniker": "NT.BIRAOCR.API\\Program.cs", "ToolTip": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\Program.cs", "RelativeToolTip": "NT.BIRAOCR.API\\Program.cs", "ViewState": "AgIAADwAAAAAAAAAAAAawFMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T23:51:54.301Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "OCRController.cs", "DocumentMoniker": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\Controllers\\OCRController.cs", "RelativeDocumentMoniker": "NT.BIRAOCR.API\\Controllers\\OCRController.cs", "ToolTip": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\Controllers\\OCRController.cs", "RelativeToolTip": "NT.BIRAOCR.API\\Controllers\\OCRController.cs", "ViewState": "AgIAABoAAAAAAAAAAAAqwCUAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T23:58:09.782Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "AuthController.cs", "DocumentMoniker": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "NT.BIRAOCR.API\\Controllers\\AuthController.cs", "ToolTip": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\Controllers\\AuthController.cs", "RelativeToolTip": "NT.BIRAOCR.API\\Controllers\\AuthController.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAABgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T23:47:34.072Z"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}]}]}]}