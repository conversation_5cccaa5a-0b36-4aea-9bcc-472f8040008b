﻿using System.Net.Http;
using Microsoft.Extensions.Options;
using PA.PAPA.DTO.Options;

namespace PA.PAPA.DataAccess.Services
{
	/// <summary>
	/// Wrapper for the http client used to call additional servers. This can go away once we move to .net core 2
	/// </summary>
	public class HttpClientService : IHttpClientService
	{
		private static HttpClient client;
		private readonly IOptions<ApiConfiguration> _apiConfigs;

		public HttpClientService(IOptions<ApiConfiguration> apiConfigs)
		{
			_apiConfigs = apiConfigs;

			client = new HttpClient();

			client.DefaultRequestHeaders.Accept.Clear();
			client.DefaultRequestHeaders.Add("Client_Id", _apiConfigs.Value.ClientId);
		}

		public HttpClient Client {
			get {
				return client;
			}
		}

		/// <summary>
		/// Combine two URL's together, accounting for leading/ending slashes
		/// </summary>
		/// <param name="url1"></param>
		/// <param name="url2"></param>
		/// <returns></returns>
		public string UrlCombine(string url1, string url2)
		{
			if (url1.Length == 0) {
				return url2;
			}

			if (url2.Length == 0) {
				return url1;
			}

			url1 = url1.TrimEnd('/', '\\');
			url2 = url2.TrimStart('/', '\\');

			return $"{url1}/{url2}";
		}
	}
}