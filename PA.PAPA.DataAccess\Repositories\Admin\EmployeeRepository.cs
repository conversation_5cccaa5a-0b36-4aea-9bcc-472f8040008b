﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.DataAccess.Repositories.Admin
{
    public class EmployeeRepository : IEmployeeRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public EmployeeRepository(IConnectionFactory connectionFactory) {
            _connectionFactory = connectionFactory;
        }


        public async Task<IEnumerable<EmployeeDTO>> Read() {
            var param = new DynamicParameters();

            using (var results = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeSelect", param, commandType: CommandType.StoredProcedure)) {
                return results.Read<EmployeeDTO>().ToList();
            }
        }

        public async Task<EmployeeDTO> Read(int id) {
            var param = new DynamicParameters();

            // pass in the organization id if we have one otherwise pass in 0 for all records
            param.Add("@EmployeeId", id);

            using (var results = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeSelect", param, commandType: CommandType.StoredProcedure)) {
                return results.Read<EmployeeDTO>().FirstOrDefault();
            }
        }

        public async Task<EmployeeDTO> Create(EmployeeDTO dto) {
            var param = new DynamicParameters();

            param.Add("@EmployeeId", dto.EmployeeId, null, ParameterDirection.InputOutput);
            param.Add("@FirstName", dto.FirstName);
            param.Add("@LastName", dto.LastName);
            param.Add("@Username", dto.UserName);
            param.Add("@EmployeeNumber", dto.EmployeeNumber);
            param.Add("@EmployeeTypeId", dto.EmployeeTypeId);
            param.Add("@IsActive", dto.IsActive);

            using (var multiResult = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeInsert", param, commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();
                dto.EmployeeId = param.Get<int>("@EmployeeId");
            }

            return await Read(dto.EmployeeId);
        }

        public async Task<EmployeeDTO> Update(EmployeeDTO dto) {
            var param = new DynamicParameters();

            param.Add("@EmployeeId", dto.EmployeeId, null, ParameterDirection.InputOutput);
            param.Add("@FirstName", dto.FirstName);
            param.Add("@LastName", dto.LastName);
            param.Add("@Username", dto.UserName);
            param.Add("@EmployeeNumber", dto.EmployeeNumber);
            param.Add("@EmployeeTypeId", dto.EmployeeTypeId);
            param.Add("@IsActive", dto.IsActive);

            using (var multiResult = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeUpdate", param, commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();
            }

            return await Read(dto.EmployeeId);
        }

        public async Task<bool> Delete(int id) {
            var param = new DynamicParameters();
            param.Add("@EmployeeId", id);
            param.Add("ReturnValue", dbType: DbType.Int32, direction: ParameterDirection.ReturnValue);
            await _connectionFactory.GetConnection.ExecuteAsync("Admin.EmployeeDelete", param, commandType: CommandType.StoredProcedure);
            bool deleted = Convert.ToBoolean(param.Get<int>("ReturnValue"));
            return deleted;
        }

        public async Task<IEnumerable<EmployeeTypeDTO>> ReadEmployeeTypes() {
            var param = new DynamicParameters();

            using (var results = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeTypeSelect", param,
                    commandType: CommandType.StoredProcedure)) {
                return results.Read<EmployeeTypeDTO>().ToList();
            }
        }

        public void Dispose() {
            _connectionFactory.Dispose();
        }
    }
}