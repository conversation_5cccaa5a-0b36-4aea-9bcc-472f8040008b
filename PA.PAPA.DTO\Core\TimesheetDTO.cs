﻿using System;
using System.Collections.Generic;
using System.Text;

namespace PA.PAPA.DTO.Core
{
    public class TimesheetDTO
    {
        public int TimesheetId { get; set; }
        public int EmployeeId { get; set; }
        public DateTime StartDate { get; set; }
        public int ProjectId { get; set; }
        public int ProjectActivityId { get; set; }
        public int? StatusId { get; set; }
        public string StatusMessage { get; set; }
        public decimal? Day1Hrs { get; set; }
        public decimal? Day2Hrs { get; set; }
        public decimal? Day3Hrs { get; set; }
        public decimal? Day4Hrs { get; set; }
        public decimal? Day5Hrs { get; set; }
        public decimal? Day6Hrs { get; set; }
        public decimal? Day7Hrs { get; set; }
        public string Day1Note { get; set; }
        public string Day2Note { get; set; }
        public string Day3Note { get; set; }
        public string Day4Note { get; set; }
        public string Day5Note { get; set; }
        public string Day6Note { get; set; }
        public string Day7Note { get; set; }   
        public int UpdatedById { get; set; }
    }
}
