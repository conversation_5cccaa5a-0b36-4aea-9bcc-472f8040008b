using System.Threading;

public static class UserHelper
{
    public static string? GetCurrentUser()
    {
        var identity = Thread.CurrentPrincipal?.Identity;
        return identity?.Name;
    }

    public static string? GetCurrentUserEmail()
    {
        var identity = Thread.CurrentPrincipal?.Identity;
        return identity?.Name;
    }

    public static string? GetCurrentUserEmployeeNumber()
    {
        var identity = Thread.CurrentPrincipal?.Identity;
        return identity?.Name;
    }
} 