﻿using Microsoft.Extensions.Options;
using PA.PAPA.DTO.Options;

namespace PA.PAPA.DataAccess.Services
{
	public class EmailService : IEmailService
	{
		private readonly IHttpClientService _httpClient;
		private readonly IOptions<ApiConfiguration> _apiConfigs;

		public EmailService(IHttpClientService httpClient, IOptions<ApiConfiguration> apiConfigs)
		{
			_httpClient = httpClient;
			_apiConfigs = apiConfigs;
		}

		//public async Task<bool> SendEmail(EmailDTO emailToSend)
		//{
		//	try {
		//		var content = new StringContent(JsonConvert.SerializeObject(emailToSend), Encoding.UTF8, "application/json");

		//		var resource = _httpClient.UrlCombine(_apiConfigs.Value.EmailServiceUrl, "/email");

		//		var response = await _httpClient.Client.PostAsync(resource, content);

		//		if (response.IsSuccessStatusCode) {
		//			var result = response.Content.ReadAsStringAsync().Result;
		//			return JsonConvert.DeserializeObject<bool>(result);
		//		}
		//		return false;
		//	}
		//	catch (Exception ex) {
		//		throw ex;
		//	}
		//}
	}
}