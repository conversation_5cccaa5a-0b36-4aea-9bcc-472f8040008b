﻿using PA.PAPA.DTO.Admin;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace PA.PAPA.Logic.Admin.Interfaces
{
    public interface IServiceLogic : IDisposable
    {
        Task<IList<ServiceDTO>> Read();
        Task<ServiceDTO> Read(int id);
        Task<ServiceDTO> Create(ServiceDTO dto);
        Task<ServiceDTO> Update(ServiceDTO dto);
        Task<ServiceDTO> Delete(int id);
    }
}
