2020-09-23 09:39:09.509 -07:00 [Information] Number of record returned 4
2020-09-23 09:45:29.798 -07:00 [Information] Number of record returned 4
2020-09-23 09:52:37.987 -07:00 [Information] Number of record returned 2
2020-09-23 09:52:38.024 -07:00 [Information] Number of record returned 4
2020-09-23 10:24:55.570 -07:00 [Information] Number of record returned 2
2020-09-23 10:24:55.727 -07:00 [Information] Number of record returned 4
2020-09-23 10:28:09.386 -07:00 [Information] Number of record returned 2
2020-09-23 10:28:09.388 -07:00 [Information] Number of record returned 4
2020-09-23 10:28:26.909 -07:00 [Information] Number of record returned 4
2020-09-23 10:28:26.934 -07:00 [Information] Number of record returned 2
2020-09-23 10:29:16.720 -07:00 [Information] Number of record returned 2
2020-09-23 10:29:16.720 -07:00 [Information] Number of record returned 4
2020-09-23 10:29:28.872 -07:00 [Information] Number of record returned 4
2020-09-23 10:29:28.876 -07:00 [Information] Number of record returned 2
2020-09-23 10:29:37.009 -07:00 [Information] Number of record returned 4
2020-09-23 10:29:37.011 -07:00 [Information] Number of record returned 2
2020-09-23 10:29:49.028 -07:00 [Information] Number of record returned 4
2020-09-23 10:29:49.070 -07:00 [Information] Number of record returned 2
2020-09-23 10:29:54.475 -07:00 [Information] Number of record returned 2
2020-09-23 10:29:54.475 -07:00 [Information] Number of record returned 4
2020-09-23 10:29:58.847 -07:00 [Information] Number of record returned 4
2020-09-23 10:29:58.857 -07:00 [Information] Number of record returned 2
2020-09-23 13:21:06.651 -07:00 [Error] Unable to resolve service for type 'PA.PAPA.API.Services.RoleService' while attempting to activate 'PA.PAPA.API.Controllers.RoleController'.
2020-09-23 13:22:11.016 -07:00 [Error] Unable to resolve service for type 'PA.PAPA.API.Services.RoleService' while attempting to activate 'PA.PAPA.API.Controllers.RoleController'.
2020-09-23 13:24:15.305 -07:00 [Error] Unable to resolve service for type 'PA.PAPA.API.Services.RoleService' while attempting to activate 'PA.PAPA.API.Controllers.RoleController'.
2020-09-23 13:41:13.358 -07:00 [Error] Element 'Id' does not match any field or property of class BooksApi.Models.Role.
2020-09-23 13:42:18.514 -07:00 [Error] Element 'Id' does not match any field or property of class BooksApi.Models.Role.
2020-09-23 14:32:45.748 -07:00 [Information] Number of record returned 2
2020-09-23 14:32:45.748 -07:00 [Information] Number of record returned 4
2020-09-23 14:57:43.787 -07:00 [Information] Number of record returned 0
2020-09-23 14:57:46.343 -07:00 [Information] Number of record returned 7
2020-09-23 14:58:38.852 -07:00 [Information] Number of record returned 7
2020-09-23 14:58:38.988 -07:00 [Information] Number of record returned 0
2020-09-23 15:09:14.204 -07:00 [Information] Number of record returned 7
2020-09-23 15:09:14.955 -07:00 [Information] Number of record returned 1
2020-09-23 15:12:58.500 -07:00 [Information] Number of record returned 1
2020-09-23 15:12:59.328 -07:00 [Information] Number of record returned 7
2020-09-23 16:39:16.409 -07:00 [Information] Number of record returned 1
2020-09-23 16:39:16.404 -07:00 [Information] Number of record returned 7
2020-09-23 16:39:25.228 -07:00 [Information] Number of record returned 7
2020-09-23 16:39:25.468 -07:00 [Information] Number of record returned 1
2020-09-23 16:39:42.497 -07:00 [Information] Number of record returned 7
2020-09-23 16:39:42.645 -07:00 [Information] Number of record returned 1
2020-09-23 16:40:20.767 -07:00 [Information] Number of record returned 7
2020-09-23 16:40:20.796 -07:00 [Information] Number of record returned 1
2020-09-23 16:40:55.262 -07:00 [Information] Number of record returned 7
2020-09-23 16:40:55.279 -07:00 [Information] Number of record returned 1
2020-09-23 16:41:08.683 -07:00 [Information] Number of record returned 7
2020-09-23 16:41:08.886 -07:00 [Information] Number of record returned 1
2020-09-23 16:41:46.834 -07:00 [Information] Number of record returned 7
2020-09-23 16:41:47.040 -07:00 [Information] Number of record returned 1
2020-09-23 16:42:00.138 -07:00 [Information] Number of record returned 7
2020-09-23 16:42:00.319 -07:00 [Information] Number of record returned 1
2020-09-23 16:42:09.347 -07:00 [Information] Number of record returned 7
2020-09-23 16:42:09.406 -07:00 [Information] Number of record returned 1
2020-09-23 16:42:16.057 -07:00 [Information] Number of record returned 7
2020-09-23 16:42:16.220 -07:00 [Information] Number of record returned 1
2020-09-23 16:42:31.870 -07:00 [Information] Number of record returned 7
2020-09-23 16:42:31.988 -07:00 [Information] Number of record returned 1
2020-09-23 16:42:59.104 -07:00 [Information] Number of record returned 7
2020-09-23 16:42:59.315 -07:00 [Information] Number of record returned 1
2020-09-23 16:43:07.229 -07:00 [Information] Number of record returned 7
2020-09-23 16:43:07.393 -07:00 [Information] Number of record returned 1
2020-09-23 16:43:17.951 -07:00 [Information] Number of record returned 7
2020-09-23 16:43:18.012 -07:00 [Information] Number of record returned 1
2020-09-23 16:43:39.765 -07:00 [Information] Number of record returned 7
2020-09-23 16:43:39.992 -07:00 [Information] Number of record returned 1
2020-09-23 16:50:48.945 -07:00 [Information] Number of record returned 7
2020-09-23 16:50:49.116 -07:00 [Information] Number of record returned 1
2020-09-23 16:50:54.854 -07:00 [Information] Number of record returned 7
2020-09-23 16:50:54.855 -07:00 [Information] Number of record returned 1
2020-09-23 19:24:23.076 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-23 19:24:54.480 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:01:21.444 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:01:21.444 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:01:28.054 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-23 20:01:36.089 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:02:05.824 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-23 20:02:37.159 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:02:37.159 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:02:37.159 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:09:28.814 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-23 20:10:00.163 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:10:31.003 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-23 20:11:02.292 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:11:28.147 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-23 20:11:56.671 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-23 20:11:56.787 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-23 20:11:58.311 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-23 20:11:59.380 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:12:03.136 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:14:24.913 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-23 20:14:36.786 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-23 20:14:56.702 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:15:03.143 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:15:03.145 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:15:03.149 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-23 20:15:33.916 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-23 20:16:05.204 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
