{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "beer-ocr-app.html", "AssetFile": "beer-ocr-app.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001872659176"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "533"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo=\""}, {"Name": "ETag", "Value": "W/\"XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU="}]}, {"Route": "beer-ocr-app.html", "AssetFile": "beer-ocr-app.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1173"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU="}]}, {"Route": "beer-ocr-app.html.gz", "AssetFile": "beer-ocr-app.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "533"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo="}]}, {"Route": "beer-ocr-app.lq6hyijkef.html", "AssetFile": "beer-ocr-app.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001872659176"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "533"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo=\""}, {"Name": "ETag", "Value": "W/\"XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lq6hyijkef"}, {"Name": "integrity", "Value": "sha256-XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU="}, {"Name": "label", "Value": "beer-ocr-app.html"}]}, {"Route": "beer-ocr-app.lq6hyijkef.html", "AssetFile": "beer-ocr-app.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1173"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lq6hyijkef"}, {"Name": "integrity", "Value": "sha256-XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU="}, {"Name": "label", "Value": "beer-ocr-app.html"}]}, {"Route": "beer-ocr-app.lq6hyijkef.html.gz", "AssetFile": "beer-ocr-app.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "533"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lq6hyijkef"}, {"Name": "integrity", "Value": "sha256-eHW0fVRDJFyXKpvwe4moBxOkx31H/1LfoqgCDZuVZUo="}, {"Name": "label", "Value": "beer-ocr-app.html.gz"}]}, {"Route": "buttonTest.5ipweew5fc.html", "AssetFile": "buttonTest.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "buttonTest.html"}]}, {"Route": "buttonTest.5ipweew5fc.html", "AssetFile": "buttonTest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "buttonTest.html"}]}, {"Route": "buttonTest.5ipweew5fc.html.gz", "AssetFile": "buttonTest.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "buttonTest.html.gz"}]}, {"Route": "buttonTest.5ipweew5fc.js", "AssetFile": "buttonTest.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "buttonTest.js"}]}, {"Route": "buttonTest.5ipweew5fc.js", "AssetFile": "buttonTest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "buttonTest.js"}]}, {"Route": "buttonTest.5ipweew5fc.js.gz", "AssetFile": "buttonTest.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "buttonTest.js.gz"}]}, {"Route": "buttonTest.html", "AssetFile": "buttonTest.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.html", "AssetFile": "buttonTest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.html.gz", "AssetFile": "buttonTest.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.js", "AssetFile": "buttonTest.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.js", "AssetFile": "buttonTest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "buttonTest.js.gz", "AssetFile": "buttonTest.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "index.4a7d5925mm.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000502765209"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1988"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Mduy9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4=\""}, {"Name": "ETag", "Value": "W/\"5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4a7d5925mm"}, {"Name": "integrity", "Value": "sha256-5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.4a7d5925mm.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8810"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4a7d5925mm"}, {"Name": "integrity", "Value": "sha256-5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.4a7d5925mm.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1988"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Mduy9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4a7d5925mm"}, {"Name": "integrity", "Value": "sha256-M<PERSON>y9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000502765209"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1988"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Mduy9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4=\""}, {"Name": "ETag", "Value": "W/\"5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8810"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1988"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Mduy9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M<PERSON>y9t6S7BFEQ5EKtqUQEETEg1rzmqetQKc9Q1voZG4="}]}, {"Route": "main.13ups3th7z.js", "AssetFile": "main.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000922509225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1083"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw=\""}, {"Name": "ETag", "Value": "W/\"cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "13ups3th7z"}, {"Name": "integrity", "Value": "sha256-cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y="}, {"Name": "label", "Value": "main.js"}]}, {"Route": "main.13ups3th7z.js", "AssetFile": "main.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4265"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "13ups3th7z"}, {"Name": "integrity", "Value": "sha256-cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y="}, {"Name": "label", "Value": "main.js"}]}, {"Route": "main.13ups3th7z.js.gz", "AssetFile": "main.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1083"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "13ups3th7z"}, {"Name": "integrity", "Value": "sha256-wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw="}, {"Name": "label", "Value": "main.js.gz"}]}, {"Route": "main.bx607eef1i.html", "AssetFile": "main.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001123595506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E=\""}, {"Name": "ETag", "Value": "W/\"8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bx607eef1i"}, {"Name": "integrity", "Value": "sha256-8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk="}, {"Name": "label", "Value": "main.html"}]}, {"Route": "main.bx607eef1i.html", "AssetFile": "main.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3160"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bx607eef1i"}, {"Name": "integrity", "Value": "sha256-8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk="}, {"Name": "label", "Value": "main.html"}]}, {"Route": "main.bx607eef1i.html.gz", "AssetFile": "main.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bx607eef1i"}, {"Name": "integrity", "Value": "sha256-Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E="}, {"Name": "label", "Value": "main.html.gz"}]}, {"Route": "main.html", "AssetFile": "main.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001123595506"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E=\""}, {"Name": "ETag", "Value": "W/\"8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk="}]}, {"Route": "main.html", "AssetFile": "main.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3160"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk="}]}, {"Route": "main.html.gz", "AssetFile": "main.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 20 May 2025 01:28:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cq1T7/QXeGEe+7wueFfcH5yKYnHmv5yCzZnhdPJYL3E="}]}, {"Route": "main.js", "AssetFile": "main.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000922509225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1083"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw=\""}, {"Name": "ETag", "Value": "W/\"cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y="}]}, {"Route": "main.js", "AssetFile": "main.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4265"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y="}]}, {"Route": "main.js.gz", "AssetFile": "main.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1083"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 00:53:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wcMeunSiPDqX0pyrS7j+w3FKXYkEnDtzuUFnvkd+sCw="}]}, {"Route": "mainForm.5ipweew5fc.css", "AssetFile": "mainForm.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "mainForm.css"}]}, {"Route": "mainForm.5ipweew5fc.css", "AssetFile": "mainForm.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "mainForm.css"}]}, {"Route": "mainForm.5ipweew5fc.css.gz", "AssetFile": "mainForm.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "mainForm.css.gz"}]}, {"Route": "mainForm.5ipweew5fc.js", "AssetFile": "mainForm.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "mainForm.js"}]}, {"Route": "mainForm.5ipweew5fc.js", "AssetFile": "mainForm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "mainForm.js"}]}, {"Route": "mainForm.5ipweew5fc.js.gz", "AssetFile": "mainForm.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "mainForm.js.gz"}]}, {"Route": "mainForm.css", "AssetFile": "mainForm.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.css", "AssetFile": "mainForm.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.css.gz", "AssetFile": "mainForm.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:54 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.html", "AssetFile": "mainForm.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000492125984"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2031"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE=\""}, {"Name": "ETag", "Value": "W/\"qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw="}]}, {"Route": "mainForm.html", "AssetFile": "mainForm.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5897"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw="}]}, {"Route": "mainForm.html.bak", "AssetFile": "mainForm.html.bak", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5887"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Ze6vaspCi2PrUT87dEqck+ymuqMKLx1xLIely4az75w=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ze6vaspCi2PrUT87dEqck+ymuqMKLx1xLIely4az75w="}]}, {"Route": "mainForm.html.gz", "AssetFile": "mainForm.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2031"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE="}]}, {"Route": "mainForm.html.z6m5epkocb.bak", "AssetFile": "mainForm.html.bak", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5887"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Ze6vaspCi2PrUT87dEqck+ymuqMKLx1xLIely4az75w=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z6m5epkocb"}, {"Name": "integrity", "Value": "sha256-Ze6vaspCi2PrUT87dEqck+ymuqMKLx1xLIely4az75w="}, {"Name": "label", "Value": "mainForm.html.bak"}]}, {"Route": "mainForm.js", "AssetFile": "mainForm.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.js", "AssetFile": "mainForm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.js.gz", "AssetFile": "mainForm.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "mainForm.lt4h0pu2ew.html", "AssetFile": "mainForm.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000492125984"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2031"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE=\""}, {"Name": "ETag", "Value": "W/\"qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lt4h0pu2ew"}, {"Name": "integrity", "Value": "sha256-qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw="}, {"Name": "label", "Value": "mainForm.html"}]}, {"Route": "mainForm.lt4h0pu2ew.html", "AssetFile": "mainForm.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5897"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lt4h0pu2ew"}, {"Name": "integrity", "Value": "sha256-qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw="}, {"Name": "label", "Value": "mainForm.html"}]}, {"Route": "mainForm.lt4h0pu2ew.html.gz", "AssetFile": "mainForm.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2031"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lt4h0pu2ew"}, {"Name": "integrity", "Value": "sha256-jzzZ9KqxRL/xqNpeDrONjeqvw8Lu74hXU3g8DtCvKUE="}, {"Name": "label", "Value": "mainForm.html.gz"}]}, {"Route": "ocrtest.7swnv70qx5.js", "AssetFile": "ocrtest.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000226757370"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4409"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc=\""}, {"Name": "ETag", "Value": "W/\"SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7swnv70qx5"}, {"Name": "integrity", "Value": "sha256-SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0="}, {"Name": "label", "Value": "ocrtest.js"}]}, {"Route": "ocrtest.7swnv70qx5.js", "AssetFile": "ocrtest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16388"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7swnv70qx5"}, {"Name": "integrity", "Value": "sha256-SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0="}, {"Name": "label", "Value": "ocrtest.js"}]}, {"Route": "ocrtest.7swnv70qx5.js.gz", "AssetFile": "ocrtest.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4409"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7swnv70qx5"}, {"Name": "integrity", "Value": "sha256-nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc="}, {"Name": "label", "Value": "ocrtest.js.gz"}]}, {"Route": "ocrtest.css", "AssetFile": "ocrtest.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000475737393"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M=\""}, {"Name": "ETag", "Value": "W/\"50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ="}]}, {"Route": "ocrtest.css", "AssetFile": "ocrtest.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7773"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ="}]}, {"Route": "ocrtest.css.gz", "AssetFile": "ocrtest.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M="}]}, {"Route": "ocrtest.html", "AssetFile": "ocrtest.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000493827160"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2024"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ=\""}, {"Name": "ETag", "Value": "W/\"qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0="}]}, {"Route": "ocrtest.html", "AssetFile": "ocrtest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5887"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0="}]}, {"Route": "ocrtest.html.gz", "AssetFile": "ocrtest.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2024"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ="}]}, {"Route": "ocrtest.jp5omh6saj.css", "AssetFile": "ocrtest.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000475737393"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M=\""}, {"Name": "ETag", "Value": "W/\"50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jp5omh6saj"}, {"Name": "integrity", "Value": "sha256-50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ="}, {"Name": "label", "Value": "ocrtest.css"}]}, {"Route": "ocrtest.jp5omh6saj.css", "AssetFile": "ocrtest.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7773"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jp5omh6saj"}, {"Name": "integrity", "Value": "sha256-50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ="}, {"Name": "label", "Value": "ocrtest.css"}]}, {"Route": "ocrtest.jp5omh6saj.css.gz", "AssetFile": "ocrtest.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:03:54 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jp5omh6saj"}, {"Name": "integrity", "Value": "sha256-8hhR/3CfyNVh2v4k7lgqE1QxFY2Mo2G2XUeOIN1J70M="}, {"Name": "label", "Value": "ocrtest.css.gz"}]}, {"Route": "ocrtest.js", "AssetFile": "ocrtest.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000226757370"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4409"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc=\""}, {"Name": "ETag", "Value": "W/\"SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0="}]}, {"Route": "ocrtest.js", "AssetFile": "ocrtest.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16388"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0="}]}, {"Route": "ocrtest.js.gz", "AssetFile": "ocrtest.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4409"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nw/GXTWi6KRVqqsyjr22Msh11b1a+3L0fJmfuzkoDJc="}]}, {"Route": "ocrtest.zh1ztznphx.html", "AssetFile": "ocrtest.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000493827160"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2024"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ=\""}, {"Name": "ETag", "Value": "W/\"qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh1ztznphx"}, {"Name": "integrity", "Value": "sha256-qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0="}, {"Name": "label", "Value": "ocrtest.html"}]}, {"Route": "ocrtest.zh1ztznphx.html", "AssetFile": "ocrtest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5887"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh1ztznphx"}, {"Name": "integrity", "Value": "sha256-qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0="}, {"Name": "label", "Value": "ocrtest.html"}]}, {"Route": "ocrtest.zh1ztznphx.html.gz", "AssetFile": "ocrtest.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2024"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:38:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zh1ztznphx"}, {"Name": "integrity", "Value": "sha256-b655BywqFps8cPOOxTEGvXp6pG1PcASGJnxLu5lG5hQ="}, {"Name": "label", "Value": "ocrtest.html.gz"}]}, {"Route": "simple-upload-test.20rbgnipeh.html", "AssetFile": "simple-upload-test.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001015228426"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "984"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI=\""}, {"Name": "ETag", "Value": "W/\"concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "20rbgnipeh"}, {"Name": "integrity", "Value": "sha256-concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0="}, {"Name": "label", "Value": "simple-upload-test.html"}]}, {"Route": "simple-upload-test.20rbgnipeh.html", "AssetFile": "simple-upload-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2877"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "20rbgnipeh"}, {"Name": "integrity", "Value": "sha256-concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0="}, {"Name": "label", "Value": "simple-upload-test.html"}]}, {"Route": "simple-upload-test.20rbgnipeh.html.gz", "AssetFile": "simple-upload-test.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "984"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "20rbgnipeh"}, {"Name": "integrity", "Value": "sha256-5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI="}, {"Name": "label", "Value": "simple-upload-test.html.gz"}]}, {"Route": "simple-upload-test.html", "AssetFile": "simple-upload-test.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001015228426"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "984"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI=\""}, {"Name": "ETag", "Value": "W/\"concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0="}]}, {"Route": "simple-upload-test.html", "AssetFile": "simple-upload-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2877"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0="}]}, {"Route": "simple-upload-test.html.gz", "AssetFile": "simple-upload-test.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "984"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 14:30:14 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5Rf4JQ2J+r0f10aosd1a6+i5Zl0+C2q8ZoDTbTceQQI="}]}, {"Route": "simpleTest.5ipweew5fc.html", "AssetFile": "simpleTest.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "simpleTest.html"}]}, {"Route": "simpleTest.5ipweew5fc.html", "AssetFile": "simpleTest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "simpleTest.html"}]}, {"Route": "simpleTest.5ipweew5fc.html.gz", "AssetFile": "simpleTest.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "simpleTest.html.gz"}]}, {"Route": "simpleTest.html", "AssetFile": "simpleTest.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "simpleTest.html", "AssetFile": "simpleTest.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "simpleTest.html.gz", "AssetFile": "simpleTest.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:56 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "styles.7ppuzqynnz.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001675041876"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "596"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8=\""}, {"Name": "ETag", "Value": "W/\"m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ppuzqynnz"}, {"Name": "integrity", "Value": "sha256-m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.7ppuzqynnz.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1599"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ppuzqynnz"}, {"Name": "integrity", "Value": "sha256-m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg="}, {"Name": "label", "Value": "styles.css"}]}, {"Route": "styles.7ppuzqynnz.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "596"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ppuzqynnz"}, {"Name": "integrity", "Value": "sha256-K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8="}, {"Name": "label", "Value": "styles.css.gz"}]}, {"Route": "styles.css", "AssetFile": "styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001675041876"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "596"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8=\""}, {"Name": "ETag", "Value": "W/\"m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg="}]}, {"Route": "styles.css", "AssetFile": "styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1599"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg="}]}, {"Route": "styles.css.gz", "AssetFile": "styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "596"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 20 May 2025 00:49:42 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K/PgMQ3CbpasHykuEQh8p1XwObA6V8qU4bLwi+gUnV8="}]}, {"Route": "upload-test.5ipweew5fc.html", "AssetFile": "upload-test.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "upload-test.html"}]}, {"Route": "upload-test.5ipweew5fc.html", "AssetFile": "upload-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "upload-test.html"}]}, {"Route": "upload-test.5ipweew5fc.html.gz", "AssetFile": "upload-test.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ipweew5fc"}, {"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}, {"Name": "label", "Value": "upload-test.html.gz"}]}, {"Route": "upload-test.html", "AssetFile": "upload-test.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "1.000000000000"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "ETag", "Value": "W/\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "upload-test.html", "AssetFile": "upload-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}, {"Route": "upload-test.html.gz", "AssetFile": "upload-test.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "0"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=\""}, {"Name": "Last-Modified", "Value": "Mon, 26 May 2025 19:55:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU="}]}]}