<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />

    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />

    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="MySql.Data" Version="9.3.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="Tesseract" Version="5.2.0" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Controllers\OCRController.cs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NT.BIRAOCR.DTO\NT.BIRAOCR.DTO.csproj" />
    <ProjectReference Include="..\NT.BIRAOCR.Logic\NT.BIRAOCR.Logic.csproj" />
    <ProjectReference Include="..\NT.BIRAOCR.DataAccess\NT.BIRAOCR.DataAccess.csproj" />
  </ItemGroup>
</Project>
