﻿using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Text;
using System.Threading.Tasks;
using FluentValidation;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DTO.Admin;
using PA.PAPA.Logic.Admin.Interfaces;

namespace PA.PAPA.Admin.Logic
{
    public class ServiceLogic : IServiceLogic
    {
        private readonly IServiceRepository _repo;

        public ServiceLogic(IServiceRepository ServiceRepository)
        {
            _repo = ServiceRepository;
        }

        public async Task<IList<ServiceDTO>> Read()
        {
            return await _repo.Read();
        }

        public async Task<ServiceDTO> Read(int id)
        {
            return await _repo.Read(id);
        }

        public async Task<ServiceDTO> Create(ServiceDTO dto)
        {
            Validate(dto);
            return await _repo.Create(dto);
        }

        public async Task<ServiceDTO> Update(ServiceDTO dto)
        {
            if (dto == null) {
                var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", "Item not set") };
                throw new FluentValidation.ValidationException("", errors);
            }

            var existing = await _repo.Read(dto.ServiceId);
            if (existing == default(ServiceDTO)) {
                var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", "Item not found") };
                throw new FluentValidation.ValidationException("", errors);
            }
            Validate(dto);

            try {
                return await _repo.Update(dto);
            }
            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", ex.ToString()) };
                    throw new FluentValidation.ValidationException("", errors);
                }
                throw;
            }
            catch (Exception) {
                throw;
            }
        }

        public async Task<ServiceDTO> Delete(int id)
        {
            return await _repo.Delete(id);
        }

        public void Dispose()
        {
        }

        public void Validate(ServiceDTO dto)
        {
            var validator = new ServiceValidator();
            validator.ValidateAndThrow(dto);
        }

        public class ServiceValidator : AbstractValidator<ServiceDTO>
        {
            public ServiceValidator()
            {
                RuleFor(x => x.Name).NotNull();
                RuleFor(x => x.Name).Length(0, 50);
            }
        }
    }
}
