2020-09-17 14:10:40.233 -07:00 [Error] Exception occurred while processing message.
System.NullReferenceException: Object reference not set to an instance of an object.
   at PA.PAPA.API.Startup.<ConfigureServices>b__6_8(TokenValidatedContext context) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Startup.cs:line 143
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents.TokenValidated(TokenValidatedContext context)
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler.HandleAuthenticateAsync()
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler.HandleAuthenticateAsync()
   at Microsoft.AspNetCore.Authentication.AuthenticationHandler`1.AuthenticateAsync()
   at Microsoft.AspNetCore.Authentication.AuthenticationService.AuthenticateAsync(HttpContext context, String scheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2020-09-17 14:10:40.263 -07:00 [Error] An unhandled exception has occurred while executing the request.
System.NullReferenceException: Object reference not set to an instance of an object.
   at PA.PAPA.API.Startup.<ConfigureServices>b__6_8(TokenValidatedContext context) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Startup.cs:line 143
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents.TokenValidated(TokenValidatedContext context)
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler.HandleAuthenticateAsync()
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler.HandleAuthenticateAsync()
   at Microsoft.AspNetCore.Authentication.AuthenticationHandler`1.AuthenticateAsync()
   at Microsoft.AspNetCore.Authentication.AuthenticationService.AuthenticateAsync(HttpContext context, String scheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2020-09-17 14:10:40.623 -07:00 [Error] Exception occurred while processing message.
System.NullReferenceException: Object reference not set to an instance of an object.
   at PA.PAPA.API.Startup.<ConfigureServices>b__6_8(TokenValidatedContext context) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Startup.cs:line 143
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents.TokenValidated(TokenValidatedContext context)
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler.HandleAuthenticateAsync()
2020-09-17 14:10:40.635 -07:00 [Error] An unhandled exception has occurred while executing the request.
System.NullReferenceException: Object reference not set to an instance of an object.
   at PA.PAPA.API.Startup.<ConfigureServices>b__6_8(TokenValidatedContext context) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Startup.cs:line 143
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents.TokenValidated(TokenValidatedContext context)
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler.HandleAuthenticateAsync()
   at Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerHandler.HandleAuthenticateAsync()
   at Microsoft.AspNetCore.Authentication.AuthenticationHandler`1.AuthenticateAsync()
   at Microsoft.AspNetCore.Authentication.AuthenticationService.AuthenticateAsync(HttpContext context, String scheme)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
