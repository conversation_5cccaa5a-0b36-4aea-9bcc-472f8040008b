﻿using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PA.PAPA.DTO;
using PA.PAPA.DTO.Admin;
using PA.PAPA.DTO.Logging;
using PA.PAPA.Logic.Admin.Interfaces;
using SmartIT.DebugHelper;

namespace PA.PAPA.API.Controllers
{
    [Route("api/[controller]")]
    //[Authorize]
    public class EmployeeController : Controller
    {
        private readonly ILogger<EmployeeController> _logger;
        private readonly IEmployeeLogic _logic;

        public EmployeeController(IEmployeeLogic employeeLogic, ILogger<EmployeeController> logger) {
            _logic = employeeLogic;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get() {
            try {
                var list = await _logic.Read();
                if (list == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetListNumberOfRecordReturned200, list.Count());
                return Ok(list);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new BaseResponse<EmployeeDTO> {
                        Messages = new List<Message> {
                            new Message {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(Events.Get, ex, "Error getting Employees: ", request.ConvertToJson());

                return request;
            }
        }

        [HttpGet("{id:int}")]
        public async Task<IActionResult> Get(int id) {
            try {
                var item = await _logic.Read(id);
                if (item == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetById200, item.ConvertToJson());
                return Ok(item);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new BaseResponse<EmployeeDTO> {
                        Messages = new List<Message> {
                            new Message {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(ex, $"Error getting Employee Id: {id}", request);

                return request;
            }
        }

        [HttpGet]
        [Route("types")]
        public async Task<IActionResult> GetEmployeeTypes() {
            try {
                var list = await _logic.ReadEmployeeTypes();
                if (list == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetListNumberOfRecordReturned200, list.Count());
                return Ok(list);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new BaseResponse<EmployeeTypeDTO> {
                        Messages = new List<Message> {
                            new Message {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(Events.Get, ex, "Error getting Employee Types: ", request.ConvertToJson());

                return request;
            }
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody] EmployeeDTO dto) {
            if (dto == null) {
                var baseResponse = new BaseResponse<EmployeeDTO>("Empty Payload", MessageType.Error);
                return BadRequest(baseResponse);
            }

            try {
                var entity = await _logic.Create(dto);
                _logger.LogInformation(Events.Post, null, Messages.PostRecordCreated_201, entity.ConvertToJson());

                return Ok(entity);
            }
            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    // Violation in one on both...
                    var baseResponse = new BaseResponse<EmployeeDTO>("Employee already exists",
                        MessageType.Error);

                    baseResponse.Messages[0].Exception = $"{ex.Message}";
                    _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson(),
                        baseResponse.ConvertToJson());

                    return BadRequest(baseResponse);
                }

                var error = new BaseResponse<EmployeeDTO>("An error has occurred processing your request.",
                    MessageType.Error);
                error.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson(),
                    error.ConvertToJson());

                return BadRequest(error);
            }
            catch (Exception ex) {
                var baseResponse = new BaseResponse<EmployeeDTO>(
                    "An error has occurred processing your request.",
                    MessageType.Error);
                baseResponse.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson());
                return BadRequest(baseResponse);
            }
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] EmployeeDTO dto) {
            if (dto == null) {
                var baseResponse = new BaseResponse<EmployeeDTO>("Empty Payload", MessageType.Error);
                return BadRequest(baseResponse);
            }

            try {
                var model = await _logic.Update(dto);
                _logger.LogInformation(Events.Put, Messages.PutRecordUpdated, dto);

                return Ok(model);
            }
            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    // Violation in one on both...
                    var baseResponse = new BaseResponse<EmployeeDTO>("Employee already exists.",
                        MessageType.Error);
                    baseResponse.Messages[0].Exception = $"{ex.Message}";
                    _logger.LogError(Events.Put, ex, Messages.PutRecordUpdateError, dto.ConvertToJson());
                    _logger.LogError(ex.Message);

                    return BadRequest(baseResponse);
                }

                var error = new BaseResponse<EmployeeDTO>("An error has occurred processing your request.",
                    MessageType.Error);
                error.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Put, ex, Messages.PutRecordUpdateError, dto.ConvertToJson(),
                    error.ConvertToJson());

                return BadRequest(error);
            }
            catch (Exception ex) {
                var baseResponse = new BaseResponse<EmployeeDTO>(
                    "An error has occurred processing your request.",
                    MessageType.Error);
                baseResponse.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Put, ex, Messages.PutRecordUpdateError, dto.ConvertToJson());

                return BadRequest(baseResponse);
            }
        }

        protected override void Dispose(bool disposing) {
            if (disposing) {
                _logic.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}