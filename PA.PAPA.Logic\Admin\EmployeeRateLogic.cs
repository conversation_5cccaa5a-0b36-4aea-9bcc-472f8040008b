﻿using System.Collections.Generic;
using System.Threading.Tasks;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DTO.Admin;
using PA.PAPA.Logic.Admin.Interfaces;

namespace PA.PAPA.Logic.Admin
{
    public class EmployeeRateLogic : IEmployeeRateLogic
    {
        private readonly IEmployeeRateRepository _repo;

        public EmployeeRateLogic(IEmployeeRateRepository EmployeeRateRepository) {
            _repo = EmployeeRateRepository;
        }

        public async Task<IEnumerable<EmployeeRateDTO>> ReadByEmployee(int employeeId) {
            return await _repo.ReadByEmployee(employeeId);
        }

        public async Task<EmployeeRateDTO> Read(int id) {
            return await _repo.Read(id);
        }

        public async Task<EmployeeRateDTO> Create(EmployeeRateDTO dto) {
            //TODO: Add data validation and other business logic

            return await _repo.Create(dto);
        }

        public async Task<EmployeeRateDTO> Update(EmployeeRateDTO dto) {
            var existing = await _repo.Read(dto.EmployeeRateId);
            if (existing == default(EmployeeRateDTO)) throw new NotFoundException();

            return await _repo.Update(dto);
        }

        //// TODO DELETE
        //public bool DuplicateExists(int organizationId, string EmployeeName)
        //{
        //    throw new NotImplementedException();
        //}

        public void Dispose() {
            // _repo?.Dispose();
        }
    }
}