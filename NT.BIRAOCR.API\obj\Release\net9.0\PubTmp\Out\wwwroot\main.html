<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>OsirTechAnalyzer - Main UI</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <h1>OsirTechAnalyzer - Main Application</h1>

    <!-- Auth Section -->
    <div id="auth-section">
      <div class="api-section">
        <h2>Register</h2>
        <form id="register-form">
          <input type="text" id="register-username" placeholder="Username" required value="testuser">
          <input type="email" id="register-email" placeholder="Email" required value="<EMAIL>">
          <input type="password" id="register-password" placeholder="Password" required value="Password123!">
          <button type="submit">Register</button>
        </form>
        <div class="response" id="register-response"></div>
      </div>
      <div class="api-section">
        <h2>Login</h2>
        <form id="login-form">
          <input type="text" id="login-username" placeholder="Username" required value="testuser">
          <input type="password" id="login-password" placeholder="Password" required value="Password123!">
          <button type="submit">Login</button>
        </form>
        <div class="response" id="login-response"></div>
      </div>
    </div>

    <!-- Main App Section (hidden until login) -->
    <div id="app-section" style="display:none;">
      <div id="token-display">
        <strong>Current Token:</strong> <span id="current-token"></span>
        <button id="logout-btn" style="float:right;">Logout</button>
      </div>
      <div class="api-section">
        <h2>Submit Analysis</h2>
        <form id="analysis-form">
          <input type="text" id="customerName" placeholder="Customer Name" required value="Test Customer">
          <input type="text" id="customerNumber" placeholder="Customer Number" required value="C12345">
          <input type="datetime-local" id="requestDate" required value="2025-05-19T10:00">
          <input type="text" id="imagePath" placeholder="Image Path" required value="/uploads/analysis-images/sample.jpg">
          <input type="text" id="location" placeholder="Location" required value="Test Location">
          <input type="text" id="menuItem" placeholder="Menu Item" required value="Test Menu Item">
          <button type="submit">Submit Analysis</button>
        </form>
        <div class="response" id="analysis-response"></div>
      </div>
      <div class="api-section">
        <h2>Upload Image</h2>
        <form id="upload-form" enctype="multipart/form-data">
          <input type="file" id="image-file" accept="image/*" required>
          <button type="submit">Upload Image</button>
        </form>
        <div class="response" id="upload-response"></div>
      </div>
      <div class="api-section">
        <h2>Validate Token</h2>
        <form id="validate-form">
          <button type="submit">Validate Token</button>
        </form>
        <div class="response" id="validate-response"></div>
      </div>
    </div>
  </div>
  <script src="main.js"></script>
</body>
</html>
