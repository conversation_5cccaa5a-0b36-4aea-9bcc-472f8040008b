using System;
using System.Security.Claims;
using System.Security.Principal;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using PA.PAPA.API.Services;
using SmartIT.DebugHelper;

namespace PA.PAPA.API.Security
{
    public class Security
    {
        private readonly Startup _startup;

        public Security(Startup startup)
        {
            _startup = startup;
        }

        internal void InitSecurity(IServiceCollection services, UserClaimService userClaimService,
            UserService userService)
        {
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Audience = _startup.Configuration["AAD:ResourceId"];
                    options.Authority =
                        $"{_startup.Configuration["AAD:Instance"]}{_startup.Configuration["AAD:TenantId"]}";
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidateIssuerSigningKey = true
                    };

                    options.Events = new JwtBearerEvents
                    {
                        OnAuthenticationFailed = context =>
                        {
                            context.Response.Redirect("/Error");
                            Console.WriteLine("OnAuthenticationFailed: " + context.Exception.Message);

                            return Task.CompletedTask;
                        },
                        OnTokenValidated = context =>
                        {
                            Console.WriteLine("OnTokenValidated: " + context.SecurityToken);
                            var claimsIdentity = (ClaimsIdentity) context.Principal.Identity;
                            var userName = Environment.UserName;
                            userName.DDump("Current User");

                            Console.WriteLine("UserName: {0}",
                                Environment.UserName + " : " + WindowsIdentity.GetCurrent());

                            var accountToken = WindowsIdentity.GetCurrent().Token;
                            Console.WriteLine("Token number is: " + accountToken);

                            // TODO Add exception handling
                            var user = _startup.UserService.GetByEmployeeNumber(userName);
                            var userClaims = _startup.UserClaimService.GetByEmployeeNumber(user.EmployeeNumber);
                            foreach (var claim in userClaims)
                                claimsIdentity.AddClaim(new Claim(claim.ClaimType, claim.ClaimValue));
                            return Task.CompletedTask;
                        }
                    };
                });

            services.AddAuthorization(options =>
            {
                options.AddPolicy("Member",
                    policy => policy.RequireClaim("MembershipId"));
                options.AddPolicy("ApiRead",
                    policy => policy.RequireClaim("EmployeeRead"));
            });
        }
    }
}