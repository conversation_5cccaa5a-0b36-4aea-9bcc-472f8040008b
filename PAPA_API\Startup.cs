using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PAPA_API.Services;

public class Startup
{
    public IConfiguration Configuration { get; }

    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;
    }

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddControllers();
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen();

        // Configure database settings
        var userDatabaseSettings = Configuration.GetSection("UserDatabaseSettings").Get<UserDatabaseSettings>();
        var userClaimDatabaseSettings = Configuration.GetSection("UserClaimDatabaseSettings").Get<UserClaimDatabaseSettings>();
        var roleDatabaseSettings = Configuration.GetSection("RoleDatabaseSettings").Get<RoleDatabaseSettings>();

        if (userDatabaseSettings != null)
        {
            services.AddSingleton(userDatabaseSettings);
            services.AddScoped<IUserService, UserService>();
        }

        if (userClaimDatabaseSettings != null)
        {
            services.AddSingleton(userClaimDatabaseSettings);
            services.AddScoped<IUserClaimService, UserClaimService>();
        }

        if (roleDatabaseSettings != null)
        {
            services.AddSingleton(roleDatabaseSettings);
            services.AddScoped<IRoleService, RoleService>();
        }
    }

    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        if (env.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseHttpsRedirection();
        app.UseRouting();
        app.UseAuthorization();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });
    }
} 