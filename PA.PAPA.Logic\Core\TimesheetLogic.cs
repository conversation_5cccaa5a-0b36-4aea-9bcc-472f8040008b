﻿using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using FluentValidation;
using FluentValidation.Results;
using Newtonsoft.Json;
using PA.PAPA.DataAccess.Repositories.Core.Interfaces;
using PA.PAPA.DTO.Core;
using PA.PAPA.Logic.Core.Interfaces;

namespace PA.PAPA.Logic.Core
{
    public class TimesheetLogic : ITimesheetLogic
    {
        private readonly ITimesheetRepository _repo;

        public TimesheetLogic(ITimesheetRepository TimesheetRepository) {
            _repo = TimesheetRepository;
        }

        public async Task<IEnumerable<TimesheetDTO>> Read(DateTime startDate, int employeeId) {
            return await _repo.Read(startDate, employeeId);
        }

        public async Task<TimesheetDTO> Read(int id) {
            return await _repo.Read(id);
        }

        //public async Task<IEnumerable<TimesheetDTO>> Save(IEnumerable<TimesheetDTO> dtos) {
        //    Validate(dtos);
        //    return await _repo.Save(dtos);
        //}


        public async Task<int> Create(Dictionary<string, string> dto ) {
            var timesheet = new TimesheetDTO();
            JsonConvert.PopulateObject(dto["values"], timesheet);

            Validate(timesheet);

            return await  _repo.Create(timesheet);

        }

        public async Task<int> Update(Dictionary<string, string> dto) {
            var key = Convert.ToInt32(dto["key"]);
            var timesheet = await _repo.Read(key);
            if (timesheet == default(TimesheetDTO)) {
                var errors = new List<ValidationFailure>() { new ValidationFailure("", "Item not found") };
                throw new ValidationException("", errors);
            }
            else {                                
                JsonConvert.PopulateObject(dto["values"], timesheet);
            }
            Validate(timesheet);

            try {
                return await _repo.Update(timesheet);
            }
            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    var errors = new List<ValidationFailure>() { new ValidationFailure("", ex.ToString()) };
                    throw new ValidationException("", errors);
                }
                throw;
            }
            catch (Exception) {
                throw;
            }

        }

        public async Task<int> Delete(int id) {
            try {
                var timesheet = await _repo.Read(id);
                if (timesheet.StatusId > 20) {
                    var errors = new List<ValidationFailure>() { new ValidationFailure("", "Timesheet cannot be deleted") };
                    throw new ValidationException("", errors);
                }

                return await _repo.Delete(id);
            }
            catch (SqlException ex) {
                if (ex.Number == 547) {  // Foreign Key violation
                    var errors = new List<ValidationFailure>() { new ValidationFailure("", ex.ToString()) };
                    throw new ValidationException("", errors);
                }
                throw;
            }
            catch (Exception) {
                throw;
            }
        }

        public void Dispose() {
        }


        public void Validate(TimesheetDTO dto) {
            var validator = new TimesheetValidator();

            validator.ValidateAndThrow(dto);

            //var failures = new List<ValidationFailure>();
            //foreach (var dto in dtos) {
            //    var result = validator.Validate(dto);
            //    if (!result.IsValid) {
            //        failures.AddRange(result.Errors);
            //    }
            //}

            //if (failures.Count > 0) {
            //    throw new ValidationException(failures);
            //}
        }




        public class TimesheetValidator : AbstractValidator<TimesheetDTO>
        {
            public TimesheetValidator() {
                RuleFor(x => x.EmployeeId).GreaterThan(0).WithMessage("Employee required");

                RuleFor(x => x.StartDate).NotNull();

                RuleFor(x => x.ProjectActivityId).GreaterThan(0).WithMessage("Project Activity required");

                RuleFor(x => x.Day1Hrs).GreaterThanOrEqualTo(0).WithMessage("Day 1 Hrs must be a positive number");
                RuleFor(x => x.Day2Hrs).GreaterThanOrEqualTo(0).WithMessage("Day 2 Hrs must be a positive number");
                RuleFor(x => x.Day3Hrs).GreaterThanOrEqualTo(0).WithMessage("Day 3 Hrs must be a positive number");
                RuleFor(x => x.Day4Hrs).GreaterThanOrEqualTo(0).WithMessage("Day 4 Hrs must be a positive number");
                RuleFor(x => x.Day5Hrs).GreaterThanOrEqualTo(0).WithMessage("Day 5 Hrs must be a positive number");
                RuleFor(x => x.Day6Hrs).GreaterThanOrEqualTo(0).WithMessage("Day 6 Hrs must be a positive number");
                RuleFor(x => x.Day7Hrs).GreaterThanOrEqualTo(0).WithMessage("Day 7 Hrs must be a positive number");

                RuleFor(x => x.Day1Hrs).LessThanOrEqualTo(24).WithMessage("Day 1 Hrs must be less than 24");
                RuleFor(x => x.Day2Hrs).LessThanOrEqualTo(24).WithMessage("Day 2 Hrs must be less than 24");
                RuleFor(x => x.Day3Hrs).LessThanOrEqualTo(24).WithMessage("Day 3 Hrs must be less than 24");
                RuleFor(x => x.Day4Hrs).LessThanOrEqualTo(24).WithMessage("Day 4 Hrs must be less than 24");
                RuleFor(x => x.Day5Hrs).LessThanOrEqualTo(24).WithMessage("Day 5 Hrs must be less than 24");
                RuleFor(x => x.Day6Hrs).LessThanOrEqualTo(24).WithMessage("Day 6 Hrs must be less than 24");
                RuleFor(x => x.Day7Hrs).LessThanOrEqualTo(24).WithMessage("Day 7 Hrs must be less than 24");


            }
        }


    }

}