/*
 * © 2025 <PERSON> | SmartIT. All Rights Reserved.
 * https://johnkocer.github.io
 * Version: 1.0.0 Date: 05-21-2025
 */

using NT.BIRAOCR.DTO;

namespace NT.BIRAOCR.Logic.Interfaces
{
    public interface IUserService
    {
        Task<AuthResponseDto> RegisterAsync(UserRegistrationDto registration);
        Task<AuthResponseDto> LoginAsync(UserLoginDto login);
        Task<bool> ValidateTokenAsync(string token);
    }
}
