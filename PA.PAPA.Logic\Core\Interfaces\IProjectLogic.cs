﻿using System;
using System.Collections.Generic;
using System.Text;
using PA.PAPA.DTO.Core;
using System.Threading.Tasks;

namespace PA.PAPA.Logic.Core.Interfaces
{
    public interface IProjectLogic : IDisposable
    {        
        Task<ProjectDTO> Read(int id);
        Task<ProjectDTO> Create(ProjectDTO dto);
        Task<ProjectDTO> Update(ProjectDTO dto);


        Task<IEnumerable<ProjectDTO>> ReadProjects();
        Task<IEnumerable<ProjectActivityDTO>> ReadProjectActivities();
        Task<IEnumerable<ProjectDTO>> ReadEmployeeProjects(int employeeId);
        Task<IEnumerable<ProjectEmployeeActivityDTO>> ReadEmployeeProjectActivities(int employeeId);

    }
}
