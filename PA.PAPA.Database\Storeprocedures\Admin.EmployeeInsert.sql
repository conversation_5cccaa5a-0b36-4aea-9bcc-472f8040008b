USE PAPA
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('Admin.EmployeeInsert') IS NOT NULL
BEGIN
  DROP PROC Admin.EmployeeInsert
  PRINT '<<< DROPPED PROC Admin.EmployeeInsert >>>'
END
GO

CREATE PROC Admin.EmployeeInsert

    @LaborUnionId INT OUT,
	@OrganizationId INT,
	@LaborUnionName VARCHAR(10),
	@LaborUnionDescription VARCHAR(30),
	@User VARCHAR(20),
	@IsActive bit = 1

AS
BEGIN

	SET NOCOUNT ON;

    DECLARE	@AuditDate DATETIME = GETUTCDATE()

    INSERT INTO Admin.LaborUnion
    (
        OrganizationId,
        LaborUnionName,
        LaborUnionDescription,
        CreatedBy,
        CreatedOn,
        UpdatedBy,
        UpdatedOn,
        IsActive
    )
	VALUES
	(	   
	    @OrganizationId,
        @LaborUnionName,
        @LaborUnionDescription,
        @User,
        @AuditDate,
        @User,
        @AuditDate,
	    1
	)

	SELECT @LaborUnionId = SCOPE_IDENTITY()
END
GO

IF OBJECT_ID('Admin.EmployeeInsert') IS NOT NULL
  PRINT '<<< CREATED PROC Admin.EmployeeInsert >>>'
ELSE
  PRINT '<<< FAILED CREATING PROC Admin.EmployeeInsert >>>'
GO

--GRANT EXEC ON Admin.EmployeeInsert TO PallsUser
GO