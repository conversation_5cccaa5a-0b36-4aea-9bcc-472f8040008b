<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OsirTechAnalyzer API Test</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <h1>OsirTechAnalyzer API Test Interface</h1>

    <div id="token-display" style="display: none;">
      <strong>Current Token:</strong> <span id="current-token">None</span>
    </div>

    <!-- Auth Section -->
    <div class="api-section">
      <h2>Authentication</h2>

      <!-- Register Endpoint -->
      <div class="endpoint">
        <h3>Register</h3>
        <textarea id="register-payload">{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "Password123!"
}</textarea>
        <button onclick="callApi('register')">Send Register Request</button>
        <div class="response" id="register-response"></div>
      </div>

      <!-- Login Endpoint -->
      <div class="endpoint">
        <h3>Login</h3>
        <textarea id="login-payload">{
  "username": "testuser",
  "password": "Password123!"
}</textarea>
        <button onclick="callApi('login')">Send Login Request</button>
        <div class="response" id="login-response"></div>
      </div>

      <!-- Validate Token Endpoint -->
      <div class="endpoint">
        <h3>Validate Token</h3>
        <div class="auth-required">
          <span>⚠️ Token Required</span>
        </div>
        <textarea id="validate-token-payload"></textarea>
        <button onclick="callApi('validate-token')">Validate Token</button>
        <div class="response" id="validate-token-response"></div>
      </div>
    </div>

    <!-- Analysis Section -->
    <div class="api-section">
      <h2>Analysis</h2>

      <!-- Submit Analysis Endpoint -->
      <div class="endpoint">
        <h3>Submit Analysis</h3>
        <div class="auth-required">
          <span>⚠️ Token Required</span>
        </div>
        <textarea id="submit-analysis-payload">{
  "customerName": "Test Customer",
  "customerNumber": "C12345",
  "requestDate": "2023-12-01T10:00:00",
  "imagePath": "/uploads/analysis-images/sample.jpg",
  "location": "Test Location",
  "menuItem": "Test Menu Item"
}</textarea>
        <button onclick="callApi('submit-analysis')">Submit Analysis</button>
        <div class="response" id="submit-analysis-response"></div>
      </div>

      <!-- Upload Image Endpoint -->
      <div class="endpoint">
        <h3>Upload Image</h3>
        <div class="auth-required">
          <span>⚠️ Token Required</span>
        </div>
        <input type="file" id="image-file" accept="image/*">
        <button onclick="uploadImage()">Upload Image</button>
        <div class="response" id="upload-image-response"></div>
      </div>
    </div>
  </div>

  <script>// Base URL for API
        const baseUrl = 'https://localhost:5001'; // Change to your API URL
        let token = null;

        // Function to show response
        function showResponse(endpoint, data) {
            const responseElement = document.getElementById(`${endpoint}-response`);
            responseElement.textContent = JSON.stringify(data, null, 2);
            responseElement.style.display = 'block';
        }

        // Function to update token display
        function updateTokenDisplay() {
            const tokenDisplay = document.getElementById('token-display');
            const currentTokenSpan = document.getElementById('current-token');

            if (token) {
                tokenDisplay.style.display = 'block';
                currentTokenSpan.textContent = token.substring(0, 20) + '...';
                // Update validate token payload
                document.getElementById('validate-token-payload').value = token;
            } else {
                tokenDisplay.style.display = 'none';
            }
        }

        // Function to call API endpoints
        async function callApi(endpoint) {
            let url, payload, options;

            try {
                switch (endpoint) {
                    case 'register':
                        url = `${baseUrl}/api/Auth/register`;
                        payload = JSON.parse(document.getElementById('register-payload').value);
                        options = {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        };
                        break;

                    case 'login':
                        url = `${baseUrl}/api/Auth/login`;
                        payload = JSON.parse(document.getElementById('login-payload').value);
                        options = {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        };
                        break;

                    case 'validate-token':
                        if (!token) {
                            showResponse(endpoint, { error: 'No token available. Please login first.' });
                            return;
                        }
                        url = `${baseUrl}/api/Auth/validate-token`;
                        payload = document.getElementById('validate-token-payload').value;
                        options = {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(payload)
                        };
                        break;

                    case 'submit-analysis':
                        if (!token) {
                            showResponse(endpoint, { error: 'No token available. Please login first.' });
                            return;
                        }
                        url = `${baseUrl}/api/Analysis/submit`;
                        payload = JSON.parse(document.getElementById('submit-analysis-payload').value);
                        options = {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(payload)
                        };
                        break;
                }

                const response = await fetch(url, options);
                const data = await response.json();

                // If login is successful, save the token
                if (endpoint === 'login' && data.token) {
                    token = data.token;
                    updateTokenDisplay();
                }

                showResponse(endpoint, data);

            } catch (error) {
                showResponse(endpoint, { error: error.message });
            }
        }

        // Function to upload image
        async function uploadImage() {
            try {
                const fileInput = document.getElementById('image-file');
                const file = fileInput.files[0];

                if (!file) {
                    showResponse('upload-image', { error: 'Please select a file to upload' });
                    return;
                }

                if (!token) {
                    showResponse('upload-image', { error: 'No token available. Please login first.' });
                    return;
                }

                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch(`${baseUrl}/api/Analysis/upload-image`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();
                showResponse('upload-image', data);

            } catch (error) {
                showResponse('upload-image', { error: error.message });
            }
        }

        // Initialize with current date
        document.addEventListener('DOMContentLoaded', function() {
            // Set current date in the analysis request
            const analysisPayload = JSON.parse(document.getElementById('submit-analysis-payload').value);
            analysisPayload.requestDate = new Date().toISOString();
            document.getElementById('submit-analysis-payload').value = JSON.stringify(analysisPayload, null, 2);
        });</script>
</body>
</html>
