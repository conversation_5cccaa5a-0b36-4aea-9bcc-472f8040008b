2020-07-27 04:04:25.634 -07:00 [Error] Error getting Employees: 
System.Data.SqlClient.SqlException (0x80131904): Procedure or function 'EmployeeSelect' expects parameter '@EmployeeId', which was not supplied.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 25
   at PA.PAPA.Logic.EmployeeLogic.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 20
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 33
ClientConnectionId:51563c30-4e00-4b65-95ee-ba664e277924
Error Number:201,State:4,Class:16
2020-07-27 04:04:36.103 -07:00 [Error] Error getting Employees: 
System.Data.SqlClient.SqlException (0x80131904): Procedure or function 'EmployeeSelect' expects parameter '@EmployeeId', which was not supplied.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 25
   at PA.PAPA.Logic.EmployeeLogic.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 20
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 33
ClientConnectionId:51563c30-4e00-4b65-95ee-ba664e277924
Error Number:201,State:4,Class:16
2020-07-27 04:05:28.874 -07:00 [Error] Error getting Employees: 
System.Data.SqlClient.SqlException (0x80131904): Procedure or function 'EmployeeSelect' expects parameter '@EmployeeId', which was not supplied.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 25
   at PA.PAPA.Logic.EmployeeLogic.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 20
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 33
ClientConnectionId:51563c30-4e00-4b65-95ee-ba664e277924
Error Number:201,State:4,Class:16
2020-07-27 04:06:32.692 -07:00 [Error] Error getting Employee Id: 2
System.Data.SqlClient.SqlException (0x80131904): Conversion failed when converting the varchar value '<EMAIL>' to data type int.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at System.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at System.Data.SqlClient.SqlDataReader.Read()
   at Dapper.SqlMapper.GridReader.ReadDeferred[T](Int32 index, Func`2 deserializer, Type effectiveType)+MoveNext() in /_/Dapper/SqlMapper.GridReader.cs:line 364
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dapper.SqlMapper.GridReader.ReadImpl[T](Type type, Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 162
   at Dapper.SqlMapper.GridReader.Read[T](Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 64
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 45
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
ClientConnectionId:51563c30-4e00-4b65-95ee-ba664e277924
Error Number:245,State:1,Class:16
2020-07-27 04:06:40.406 -07:00 [Error] Error getting Employees: 
System.Data.SqlClient.SqlException (0x80131904): Procedure or function 'EmployeeSelect' expects parameter '@EmployeeId', which was not supplied.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 25
   at PA.PAPA.Logic.EmployeeLogic.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 20
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 33
ClientConnectionId:51563c30-4e00-4b65-95ee-ba664e277924
Error Number:201,State:4,Class:16
2020-07-27 04:08:31.168 -07:00 [Error] Error getting Employees: 
System.Data.SqlClient.SqlException (0x80131904): Procedure or function 'EmployeeSelect' expects parameter '@EmployeeId', which was not supplied.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 25
   at PA.PAPA.Logic.EmployeeLogic.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 20
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 33
ClientConnectionId:51563c30-4e00-4b65-95ee-ba664e277924
Error Number:201,State:4,Class:16
2020-07-27 04:10:20.427 -07:00 [Error] Error getting Employees: 
System.Data.SqlClient.SqlException (0x80131904): Conversion failed when converting the varchar value '<EMAIL>' to data type int.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at System.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at System.Data.SqlClient.SqlDataReader.Read()
   at Dapper.SqlMapper.GridReader.ReadDeferred[T](Int32 index, Func`2 deserializer, Type effectiveType)+MoveNext() in /_/Dapper/SqlMapper.GridReader.cs:line 364
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dapper.SqlMapper.GridReader.ReadImpl[T](Type type, Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 162
   at Dapper.SqlMapper.GridReader.Read[T](Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 64
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 29
   at PA.PAPA.Logic.EmployeeLogic.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 20
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 33
ClientConnectionId:efcf67b1-2bdc-47ee-931e-2cdaf8106979
Error Number:245,State:1,Class:16
2020-07-27 04:14:44.485 -07:00 [Error] Error getting Employees: 
System.Data.SqlClient.SqlException (0x80131904): Conversion failed when converting the varchar value '<EMAIL>' to data type int.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at System.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at System.Data.SqlClient.SqlDataReader.Read()
   at Dapper.SqlMapper.GridReader.ReadDeferred[T](Int32 index, Func`2 deserializer, Type effectiveType)+MoveNext() in /_/Dapper/SqlMapper.GridReader.cs:line 364
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dapper.SqlMapper.GridReader.ReadImpl[T](Type type, Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 162
   at Dapper.SqlMapper.GridReader.Read[T](Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 64
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 29
   at PA.PAPA.Logic.EmployeeLogic.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 20
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 33
ClientConnectionId:08db473c-3978-434b-b591-5a047cfcb5e7
Error Number:245,State:1,Class:16
2020-07-27 04:21:33.582 -07:00 [Error] Error getting Employees: 
System.Data.SqlClient.SqlException (0x80131904): Conversion failed when converting the varchar value '<EMAIL>' to data type int.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at System.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at System.Data.SqlClient.SqlDataReader.Read()
   at Dapper.SqlMapper.GridReader.ReadDeferred[T](Int32 index, Func`2 deserializer, Type effectiveType)+MoveNext() in /_/Dapper/SqlMapper.GridReader.cs:line 364
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dapper.SqlMapper.GridReader.ReadImpl[T](Type type, Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 162
   at Dapper.SqlMapper.GridReader.Read[T](Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 64
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read()
   at PA.PAPA.Logic.EmployeeLogic.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 20
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 33
ClientConnectionId:66a2697c-3a31-4781-b06a-6106fd265db6
Error Number:245,State:1,Class:16
2020-07-27 04:24:08.101 -07:00 [Information] Number of record returnned 2
2020-07-27 04:24:27.224 -07:00 [Information] Number of record returnned 2
2020-07-27 04:24:35.243 -07:00 [Error] Error getting Employee Id: 2
System.Data.SqlClient.SqlException (0x80131904): Conversion failed when converting the varchar value '<EMAIL>' to data type int.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at System.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at System.Data.SqlClient.SqlDataReader.Read()
   at Dapper.SqlMapper.GridReader.ReadDeferred[T](Int32 index, Func`2 deserializer, Type effectiveType)+MoveNext() in /_/Dapper/SqlMapper.GridReader.cs:line 364
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dapper.SqlMapper.GridReader.ReadImpl[T](Type type, Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 162
   at Dapper.SqlMapper.GridReader.Read[T](Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 64
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 45
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
ClientConnectionId:96666825-eb4f-417a-8cd2-912171060a36
Error Number:245,State:1,Class:16
2020-07-27 04:24:50.521 -07:00 [Error] Error getting Employee Id: 2
System.Data.SqlClient.SqlException (0x80131904): Conversion failed when converting the varchar value '<EMAIL>' to data type int.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at System.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at System.Data.SqlClient.SqlDataReader.Read()
   at Dapper.SqlMapper.GridReader.ReadDeferred[T](Int32 index, Func`2 deserializer, Type effectiveType)+MoveNext() in /_/Dapper/SqlMapper.GridReader.cs:line 364
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dapper.SqlMapper.GridReader.ReadImpl[T](Type type, Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 162
   at Dapper.SqlMapper.GridReader.Read[T](Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 64
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 45
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
ClientConnectionId:96666825-eb4f-417a-8cd2-912171060a36
Error Number:245,State:1,Class:16
2020-07-27 04:25:26.454 -07:00 [Error] Error getting Employee Id: 2
System.Data.SqlClient.SqlException (0x80131904): Conversion failed when converting the varchar value '<EMAIL>' to data type int.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at System.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at System.Data.SqlClient.SqlDataReader.Read()
   at Dapper.SqlMapper.GridReader.ReadDeferred[T](Int32 index, Func`2 deserializer, Type effectiveType)+MoveNext() in /_/Dapper/SqlMapper.GridReader.cs:line 364
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Dapper.SqlMapper.GridReader.ReadImpl[T](Type type, Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 162
   at Dapper.SqlMapper.GridReader.Read[T](Boolean buffered) in /_/Dapper/SqlMapper.GridReader.cs:line 64
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 45
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
ClientConnectionId:96666825-eb4f-417a-8cd2-912171060a36
Error Number:245,State:1,Class:16
2020-07-27 04:26:14.444 -07:00 [Error] Error getting Employee Id: 2
System.InvalidCastException: Unable to cast object of type 'System.Collections.Generic.List`1[PA.PAPA.DTO.Admin.EmployeeDTO]' to type 'PA.PAPA.DTO.Admin.EmployeeDTO'.
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 45
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
2020-07-27 04:29:46.069 -07:00 [Error] Error getting Employee Id: 2
System.InvalidCastException: Unable to cast object of type 'System.Collections.Generic.List`1[PA.PAPA.DTO.Admin.EmployeeDTO]' to type 'PA.PAPA.DTO.Admin.EmployeeDTO'.
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 45
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
2020-07-27 04:31:17.136 -07:00 [Error] Error getting Employee Id: 2
System.InvalidCastException: Unable to cast object of type 'System.Collections.Generic.List`1[PA.PAPA.DTO.Admin.EmployeeDTO]' to type 'PA.PAPA.DTO.Admin.EmployeeDTO'.
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 45
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
2020-07-27 04:31:22.325 -07:00 [Error] Error getting Employee Id: 2
System.InvalidCastException: Unable to cast object of type 'System.Collections.Generic.List`1[PA.PAPA.DTO.Admin.EmployeeDTO]' to type 'PA.PAPA.DTO.Admin.EmployeeDTO'.
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 45
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
2020-07-27 04:32:32.592 -07:00 [Error] Error getting Employee Id: 2
Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: Cannot perform runtime binding on a null reference
   at CallSite.Target(Closure , CallSite , Object )
   at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 41
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
2020-07-27 04:34:00.348 -07:00 [Information] Number of record returnned 2
2020-07-27 04:34:18.901 -07:00 [Error] Error getting Employee Id: 3
Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: Cannot perform runtime binding on a null reference
   at CallSite.Target(Closure , CallSite , Object )
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 41
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
2020-07-27 04:37:42.081 -07:00 [Error] Error getting Employee Id: 2
Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: Cannot perform runtime binding on a null reference
   at CallSite.Target(Closure , CallSite , Object )
   at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 41
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
2020-07-27 04:38:01.701 -07:00 [Error] Error getting Employee Id: 2
Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: Cannot perform runtime binding on a null reference
   at CallSite.Target(Closure , CallSite , Object )
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 41
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
2020-07-27 04:45:59.683 -07:00 [Error] Error getting Employee Id: 2
Microsoft.CSharp.RuntimeBinder.RuntimeBinderException: Cannot perform runtime binding on a null reference
   at CallSite.Target(Closure , CallSite , Object )
   at System.Dynamic.UpdateDelegates.UpdateAndExecute1[T0,TRet](CallSite site, T0 arg0)
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 41
   at PA.PAPA.Logic.EmployeeLogic.Read(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 29
   at PA.PAPA.API.Controllers.EmployeeController.Get(Int32 id) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 64
2020-07-27 04:51:08.304 -07:00 [Information] Number of record returnned "{\"EmployeeId\":2,\"FirstName\":\"Mark\",\"MiddleName\":\"A\",\"LastName\":\"Delcambre\",\"UserName\":\"markd\",\"EmployeeNumber\":\"300090\",\"EmployeeTypeId\":\"1\",\"IsActive\":true}"
2020-07-27 04:52:45.542 -07:00 [Information] Number of record returnned "{\"EmployeeId\":3,\"FirstName\":\"John\",\"MiddleName\":\"L\",\"LastName\":\"Bonano\",\"UserName\":\"johnb\",\"EmployeeNumber\":\"300065\",\"EmployeeTypeId\":\"1\",\"IsActive\":true}"
2020-07-27 04:53:03.725 -07:00 [Information] Number of record returnned "{\"EmployeeId\":2,\"FirstName\":\"Mark\",\"MiddleName\":\"A\",\"LastName\":\"Delcambre\",\"UserName\":\"markd\",\"EmployeeNumber\":\"300090\",\"EmployeeTypeId\":\"1\",\"IsActive\":true}"
2020-07-27 04:54:45.853 -07:00 [Error] Entity Create Error:  "{\"EmployeeId\":0,\"FirstName\":\"John\",\"MiddleName\":null,\"LastName\":\"Kocer\",\"UserName\":\"johnko\",\"EmployeeNumber\":\"300090\",\"EmployeeTypeId\":null,\"IsActive\":true}" : BaseResponse: "{\"Entities\":[],\"Messages\":[{\"Text\":\"An error has occurred processing your request.\",\"Exception\":\"Procedure or function 'EmployeeInsert' expects parameter '@MiddleName', which was not supplied.\",\"Type\":3}]}"
System.Data.SqlClient.SqlException (0x80131904): Procedure or function 'EmployeeInsert' expects parameter '@MiddleName', which was not supplied.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Create(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 63
   at PA.PAPA.Logic.EmployeeLogic.Create(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 36
   at PA.PAPA.API.Controllers.EmployeeController.Post(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 102
ClientConnectionId:8ba760ec-9ddd-465f-a6c6-9ecbd6d82476
Error Number:201,State:4,Class:16
2020-07-27 05:03:00.420 -07:00 [Error] Entity Create Error:  "{\"EmployeeId\":0,\"FirstName\":\"John\",\"MiddleName\":null,\"LastName\":\"Kocer\",\"UserName\":\"johnko\",\"EmployeeNumber\":\"300090\",\"EmployeeTypeId\":0,\"IsActive\":true}" : BaseResponse: "{\"Entities\":[],\"Messages\":[{\"Text\":\"An error has occurred processing your request.\",\"Exception\":\"Procedure or function 'EmployeeInsert' expects parameter '@MiddleName', which was not supplied.\",\"Type\":3}]}"
System.Data.SqlClient.SqlException (0x80131904): Procedure or function 'EmployeeInsert' expects parameter '@MiddleName', which was not supplied.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Create(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 63
   at PA.PAPA.Logic.EmployeeLogic.Create(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 36
   at PA.PAPA.API.Controllers.EmployeeController.Post(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 102
ClientConnectionId:be21e0c4-4262-405a-b13b-e7579d88daa6
Error Number:201,State:4,Class:16
2020-07-27 05:04:02.540 -07:00 [Error] Entity Create Error:  "{\"EmployeeId\":0,\"FirstName\":\"John\",\"MiddleName\":null,\"LastName\":\"Kocer\",\"UserName\":\"johnko\",\"EmployeeNumber\":\"300080\",\"EmployeeTypeId\":0,\"IsActive\":true}" : BaseResponse: "{\"Entities\":[],\"Messages\":[{\"Text\":\"An error has occurred processing your request.\",\"Exception\":\"Procedure or function 'EmployeeInsert' expects parameter '@MiddleName', which was not supplied.\",\"Type\":3}]}"
System.Data.SqlClient.SqlException (0x80131904): Procedure or function 'EmployeeInsert' expects parameter '@MiddleName', which was not supplied.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Create(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 63
   at PA.PAPA.Logic.EmployeeLogic.Create(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 36
   at PA.PAPA.API.Controllers.EmployeeController.Post(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 102
ClientConnectionId:be21e0c4-4262-405a-b13b-e7579d88daa6
Error Number:201,State:4,Class:16
2020-07-27 05:06:45.047 -07:00 [Error] Entity Create Error:  "{\"EmployeeId\":0,\"FirstName\":\"John\",\"MiddleName\":null,\"LastName\":\"Kocer\",\"UserName\":\"johnko\",\"EmployeeNumber\":\"300080\",\"EmployeeTypeId\":0,\"IsActive\":true}" : BaseResponse: "{\"Entities\":[],\"Messages\":[{\"Text\":\"An error has occurred processing your request.\",\"Exception\":\"Procedure or function 'EmployeeInsert' expects parameter '@MiddleName', which was not supplied.\",\"Type\":3}]}"
System.Data.SqlClient.SqlException (0x80131904): Procedure or function 'EmployeeInsert' expects parameter '@MiddleName', which was not supplied.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Create(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 63
   at PA.PAPA.Logic.EmployeeLogic.Create(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 36
   at PA.PAPA.API.Controllers.EmployeeController.Post(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 102
ClientConnectionId:be21e0c4-4262-405a-b13b-e7579d88daa6
Error Number:201,State:4,Class:16
2020-07-27 05:06:58.114 -07:00 [Information] Number of record returnned 2
2020-07-27 05:08:08.809 -07:00 [Error] Entity Create Error:  "{\"EmployeeId\":0,\"FirstName\":\"John\",\"MiddleName\":\"L\",\"LastName\":\"Kocer\",\"UserName\":\"johnko\",\"EmployeeNumber\":\"300066\",\"EmployeeTypeId\":1,\"IsActive\":true}" : BaseResponse: "{\"Entities\":[],\"Messages\":[{\"Text\":\"An error has occurred processing your request.\",\"Exception\":\"Procedure or function 'EmployeeInsert' expects parameter '@MiddleName', which was not supplied.\",\"Type\":3}]}"
System.Data.SqlClient.SqlException (0x80131904): Procedure or function 'EmployeeInsert' expects parameter '@MiddleName', which was not supplied.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Create(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 63
   at PA.PAPA.Logic.EmployeeLogic.Create(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 36
   at PA.PAPA.API.Controllers.EmployeeController.Post(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 102
ClientConnectionId:be21e0c4-4262-405a-b13b-e7579d88daa6
Error Number:201,State:4,Class:16
2020-07-27 05:10:15.033 -07:00 [Information] Record created : "{\"EmployeeId\":4,\"FirstName\":\"John\",\"MiddleName\":null,\"LastName\":\"Kocer\",\"UserName\":\"johnko\",\"EmployeeNumber\":\"300080\",\"EmployeeTypeId\":1,\"IsActive\":true}"
2020-07-27 05:13:56.665 -07:00 [Information] Number of record returnned 3
2020-07-27 05:13:57.869 -07:00 [Information] Number of record returnned 3
2020-07-27 05:13:59.899 -07:00 [Information] Number of record returnned 3
2020-07-27 05:14:10.478 -07:00 [Information] Number of record returnned 3
2020-07-27 05:14:29.874 -07:00 [Information] Number of record returnned 3
2020-07-27 05:15:04.601 -07:00 [Error] Entity update Error : "{\"EmployeeId\":4,\"FirstName\":\"John\",\"MiddleName\":null,\"LastName\":\"Kocer\",\"UserName\":\"johnko\",\"EmployeeNumber\":\"300044\",\"EmployeeTypeId\":1,\"IsActive\":true}" : BaseResponse: "{\"Entities\":[],\"Messages\":[{\"Text\":\"An error has occurred processing your request.\",\"Exception\":\"Procedure or function EmployeeUpdate has too many arguments specified.\",\"Type\":3}]}"
System.Data.SqlClient.SqlException (0x80131904): Procedure or function EmployeeUpdate has too many arguments specified.
   at System.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__126_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location where exception was thrown ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1050
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Update(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 90
   at PA.PAPA.Logic.EmployeeLogic.Update(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\EmployeeLogic.cs:line 48
   at PA.PAPA.API.Controllers.EmployeeController.Put(EmployeeDTO dto) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 154
ClientConnectionId:d5d3d4bf-07da-45c2-a013-841de8d0ef84
Error Number:8144,State:2,Class:16
2020-07-27 05:20:03.176 -07:00 [Information] Record Updated: "PA.PAPA.DTO.Admin.EmployeeDTO"
2020-07-27 05:20:08.437 -07:00 [Information] Number of record returnned 3
2020-07-27 05:26:52.545 -07:00 [Information] Number of record returnned 3
2020-07-27 05:26:55.188 -07:00 [Information] Number of record returnned 3
2020-07-27 05:26:57.835 -07:00 [Information] Number of record returnned 3
2020-07-27 05:27:01.524 -07:00 [Information] Number of record returnned 3
2020-07-27 05:27:06.107 -07:00 [Information] Number of record returnned 3
2020-07-27 05:27:08.901 -07:00 [Information] Number of record returnned 3
2020-07-27 05:27:16.190 -07:00 [Information] Number of record returnned 3
2020-07-27 05:27:18.457 -07:00 [Information] Number of record returnned 3
2020-07-27 05:27:22.447 -07:00 [Information] Number of record returnned 3
2020-07-27 05:27:25.569 -07:00 [Information] Number of record returnned 3
2020-07-27 05:27:44.367 -07:00 [Information] Number of record returnned 3
2020-07-27 05:28:01.900 -07:00 [Information] Number of record returnned 3
2020-07-27 05:28:13.101 -07:00 [Information] Number of record returnned 3
2020-07-27 05:28:21.342 -07:00 [Information] Number of record returnned 3
2020-07-27 05:28:29.239 -07:00 [Information] Number of record returnned "{\"EmployeeId\":2,\"FirstName\":\"Mark\",\"MiddleName\":\"A\",\"LastName\":\"Delcambre\",\"UserName\":\"markd\",\"EmployeeNumber\":\"300090\",\"EmployeeTypeId\":1,\"IsActive\":true}"
2020-07-27 05:29:30.304 -07:00 [Information] Number of record returnned 3
2020-07-27 05:31:11.952 -07:00 [Information] Number of record returnned 3
2020-07-27 05:31:15.007 -07:00 [Information] Number of record returnned 3
2020-07-27 05:32:48.642 -07:00 [Information] Number of record returnned 3
2020-07-27 05:33:41.462 -07:00 [Information] Number of record returnned "{\"EmployeeId\":2,\"FirstName\":\"Mark\",\"MiddleName\":\"A\",\"LastName\":\"Delcambre\",\"UserName\":\"markd\",\"EmployeeNumber\":\"300090\",\"EmployeeTypeId\":1,\"IsActive\":true}"
2020-07-27 14:24:18.197 -07:00 [Information] Number of record returnned 3
2020-07-27 14:29:53.220 -07:00 [Information] Number of record returnned 3
2020-07-27 14:29:53.696 -07:00 [Information] Number of record returnned 3
2020-07-27 14:29:53.901 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:06.106 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:06.489 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:06.697 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:15.135 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:15.336 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:15.609 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:19.202 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:19.415 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:19.583 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:37.041 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:37.444 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:37.681 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:38.566 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:41.099 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:41.328 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:41.490 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:52.570 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:52.812 -07:00 [Information] Number of record returnned 3
2020-07-27 14:30:53.017 -07:00 [Information] Number of record returnned 3
2020-07-27 14:31:00.291 -07:00 [Information] Number of record returnned 3
2020-07-27 14:31:02.201 -07:00 [Information] Number of record returnned 3
2020-07-27 14:31:03.469 -07:00 [Information] Number of record returnned 3
2020-07-27 14:31:31.159 -07:00 [Information] Number of record returnned 3
2020-07-27 14:31:31.416 -07:00 [Information] Number of record returnned 3
2020-07-27 14:31:31.716 -07:00 [Information] Number of record returnned 3
2020-07-27 14:32:07.145 -07:00 [Information] Number of record returnned "{\"EmployeeId\":2,\"FirstName\":\"Mark\",\"MiddleName\":\"A\",\"LastName\":\"Delcambre\",\"UserName\":\"markd\",\"EmployeeNumber\":\"300090\",\"EmployeeTypeId\":1,\"IsActive\":true}"
2020-07-27 14:38:28.195 -07:00 [Information] Number of record returnned 3
2020-07-27 14:49:12.903 -07:00 [Information] Number of record returnned "{\"EmployeeId\":2,\"FirstName\":\"Mark\",\"MiddleName\":\"A\",\"LastName\":\"Delcambre\",\"UserName\":\"markd\",\"EmployeeNumber\":\"300090\",\"EmployeeTypeId\":1,\"IsActive\":true}"
2020-07-27 15:13:53.074 -07:00 [Information] Number of record returnned 3
2020-07-27 15:13:53.250 -07:00 [Information] Number of record returnned 3
2020-07-27 15:13:53.374 -07:00 [Information] Number of record returnned 3
2020-07-27 15:56:32.160 -07:00 [Information] Number of record returnned "{\"EmployeeId\":2,\"FirstName\":\"Mark\",\"MiddleName\":\"A\",\"LastName\":\"Delcambre\",\"UserName\":\"markd\",\"EmployeeNumber\":\"300090\",\"EmployeeTypeId\":1,\"IsActive\":true}"
2020-07-27 16:35:20.628 -07:00 [Information] Number of record returnned "{\"EmployeeId\":3,\"FirstName\":\"John\",\"MiddleName\":\"L\",\"LastName\":\"Bonano\",\"UserName\":\"johnb\",\"EmployeeNumber\":\"300065\",\"EmployeeTypeId\":1,\"IsActive\":true}"
