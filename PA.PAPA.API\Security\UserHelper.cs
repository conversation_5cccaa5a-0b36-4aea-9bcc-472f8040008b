using System.Linq;
using System.Security.Claims;
using System.Threading;

namespace PA.PAPA.API
{
    public class UserHelper
    {
        public static string GetUserId()
        {
            var identity = (ClaimsPrincipal) Thread.CurrentPrincipal;
            var principal = Thread.CurrentPrincipal as ClaimsPrincipal;
            var userId = identity.Claims.Where(c => c.Type == ClaimTypes.NameIdentifier).Select(c => c.Value)
                .SingleOrDefault();
            return userId;
        }

        public static string GetUserName()
        {
            var identity = (ClaimsPrincipal) Thread.CurrentPrincipal;
            var principal = Thread.CurrentPrincipal as ClaimsPrincipal;
            var name = identity.Claims.Where(c => c.Type == ClaimTypes.Name).Select(c => c.Value).SingleOrDefault();
            return name;
        }

        public static string GetUserMail()
        {
            var identity = (ClaimsPrincipal) Thread.CurrentPrincipal;
            var principal = Thread.CurrentPrincipal as ClaimsPrincipal;
            var mail = identity.Claims.Where(c => c.Type == ClaimTypes.Email).Select(c => c.Value).SingleOrDefault();
            return mail;
        }

    }
}