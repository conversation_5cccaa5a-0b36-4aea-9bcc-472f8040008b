using System.Net;
using Microsoft.Data.SqlClient;
using MySql.Data.MySqlClient;
using System.Data.SqlClient;
using NT.BIRAOCR.DataAccess;
using NT.BIRAOCR.Logic.Interfaces;
using NT.BIRAOCR.Logic.Services;

namespace NT.BIRAOCR.API
{
  public class Program
  {
    public static void Main(string[] args)
    {
      var builder = WebApplication.CreateBuilder(args);

      // Add services to the container.
      builder.Services.AddControllers();
      builder.Services.AddEndpointsApiExplorer();
      builder.Services.AddSwaggerGen();

      // Register IConnectionFactory and IDbConnection
      builder.Services.AddScoped<NT.BIRAOCR.DataAccess.IConnectionFactory, NT.BIRAOCR.DataAccess.ConnectionFactory>();
      builder.Services.AddScoped<System.Data.IDbConnection>(provider =>
      {
        var configuration = provider.GetRequiredService<IConfiguration>();
        var connectionString = configuration.GetConnectionString("DefaultConnection"); // Ensure this key exists in appsettings.json
        return new Microsoft.Data.SqlClient.SqlConnection(connectionString); // Use Microsoft.Data.SqlClient.SqlConnection
      });

      // Register your business logic services
      builder.Services.AddScoped<IUserService, UserService>();
      builder.Services.AddScoped<IAnalysisService, AnalysisService>();
      builder.Services.AddScoped<IBlobStorageService, BlobStorageService>();

      builder.Services.AddCors(options =>
      {
        options.AddDefaultPolicy(policy =>
        {
          policy.AllowAnyOrigin()
                .AllowAnyHeader()
                .AllowAnyMethod();
        });
      });

      var app = builder.Build();
      app.UseWebSockets();

      // Configure the HTTP request pipeline.
      if (app.Environment.IsDevelopment())
      {
        app.UseSwagger();
        app.UseSwaggerUI();

        // Add middleware to handle browser refresh requests
        app.Use(async (context, next) =>
        {
          if (context.Request.Path.StartsWithSegments("/_framework") ||
              context.Request.Path.StartsWithSegments("/_vs"))
          {
            context.Response.StatusCode = 404;
            return;
          }

          await next();
        });
      }

      app.UseHttpsRedirection();

      // Serve static files
      app.UseStaticFiles();

      app.UseCors(); // Enable CORS with default policy
      app.UseAuthorization();
      app.MapControllers();

      // Redirect root to Swagger UI
      app.MapGet("/", () => Results.Redirect("/swagger"));

      app.Run();
    }
  }
}