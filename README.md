# MenuScan - OCR Menu Analysis Solution

## Overview

MenuScan is a comprehensive .NET 9 solution designed for extracting and analyzing text from German drink menus using advanced OCR (Optical Character Recognition) technology. The solution combines Tesseract OCR with OpenAI's GPT-4 Vision API to provide accurate menu item extraction and price formatting.

### Key Features

- **Advanced OCR Processing**: Tesseract-based text extraction with German language support
- **AI-Powered Analysis**: OpenAI GPT-4 Vision integration for intelligent menu parsing
- **Multi-Language Support**: German, English, and Turkish OCR capabilities
- **Web Interface**: User-friendly HTML interface for image upload and processing
- **RESTful API**: Comprehensive API endpoints for integration
- **JWT Authentication**: Secure authentication system
- **Multi-Database Support**: SQL Server and MySQL compatibility
- **Employee Management**: Additional PAPA API for project and employee management

### Target Audience

- Developers building menu digitization solutions
- Restaurant technology integrators
- OCR application developers
- DevOps engineers deploying text extraction services

## Technical Specifications

### Tech Stack

- **Backend**: .NET 9 (ASP.NET Core Web API)
- **OCR Engine**: Tesseract 5.2.0
- **AI Integration**: OpenAI GPT-4 Vision API
- **Authentication**: JWT Bearer tokens
- **Databases**: 
  - SQL Server (primary)
  - MySQL (supported)
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Package Management**: NuGet
- **Development Environment**: Visual Studio 2022

### Architecture

The solution follows a layered architecture pattern:

```
┌─────────────────┐
│   Controllers   │  ← API endpoints and request handling
├─────────────────┤
│     Logic       │  ← Business logic and services
├─────────────────┤
│   DataAccess    │  ← Database operations and repositories
├─────────────────┤
│      DTO        │  ← Data transfer objects
└─────────────────┘
```

### Project Structure

- **NT.BIRAOCR.API**: Main OCR API application
- **NT.BIRAOCR.Logic**: Business logic and services
- **NT.BIRAOCR.DataAccess**: Database access layer
- **NT.BIRAOCR.DTO**: Data transfer objects and models
- **NT.BIRAOCR.Database**: Database schema and scripts
- **PA.PAPA.API**: Employee and project management API
- **PAPA_API**: Additional API services

### Dependencies

- **Microsoft.AspNetCore.OpenApi** (9.0.5)
- **Microsoft.AspNetCore.Authentication.JwtBearer** (9.0.5)
- **Microsoft.Data.SqlClient** (6.0.2)
- **MySql.Data** (9.3.0)
- **Tesseract** (5.2.0)
- **Swashbuckle.AspNetCore** (8.1.1)

## Setup & Installation

### Prerequisites

- **.NET 9 SDK** or later
- **Visual Studio 2022** (recommended) or VS Code
- **SQL Server** or **MySQL** database
- **OpenAI API Key** (for GPT-4 Vision features)

### Environment Setup

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd MenuScan
   ```

2. **Install dependencies**:
   ```bash
   dotnet restore
   ```

3. **Database Configuration**:
   
   Update connection strings in `appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=localhost;Database=BIRAOCR;User Id=sa;Password=YourPassword;TrustServerCertificate=True;"
     }
   }
   ```

4. **OpenAI Configuration**:
   
   Add your OpenAI API key to `appsettings.json`:
   ```json
   {
     "OpenAI": {
       "ApiKey": "your-openai-api-key-here"
     }
   }
   ```

5. **JWT Configuration**:
   
   Configure JWT settings:
   ```json
   {
     "Jwt": {
       "Key": "YourSecretKeyForAuthenticationOfApplicationBIRAOCR",
       "Issuer": "BIRAOCR",
       "Audience": "BIRAOCR",
       "ExpiryMinutes": 10080
     }
   }
   ```

### Database Setup

Run the database setup script located at:
`NT.BIRAOCR.DTO\Scripts\Database_Setup.sql`

This will create the necessary tables and stored procedures.

## Usage

### Running the Application

1. **Development Mode**:
   ```bash
   cd NT.BIRAOCR.API
   dotnet run
   ```

2. **Using Visual Studio**:
   - Open `MenuScan.sln`
   - Set `NT.BIRAOCR.API` as startup project
   - Press F5 or click "Start"

3. **Access the application**:
   - **HTTPS**: https://localhost:5001
   - **HTTP**: http://localhost:5000
   - **Swagger UI**: https://localhost:5001/swagger

### Web Interface

Navigate to the web interface:
- **Main Application**: https://localhost:5001/beer-ocr-app.html
- **OCR Test Interface**: https://localhost:5001/ocrtest.html

### API Endpoints

#### OCR Controller

**Extract Text from Image**:
```http
POST /api/OCR/extract
Content-Type: multipart/form-data

{
  "image": [image file]
}
```

**Extract with AI Analysis**:
```http
POST /api/OCR/extractTest
Content-Type: multipart/form-data

{
  "image": [image file],
  "prompt": "optional custom prompt",
  "userText": "optional user text"
}
```

#### Analysis Controller

**Submit Analysis Request**:
```http
POST /api/Analysis/submit
Content-Type: application/json

{
  "customerName": "string",
  "customerNumber": "string",
  "requestDate": "2024-01-01T00:00:00",
  "imagePath": "string",
  "location": "string",
  "menuItem": "string"
}
```

### Example Usage

1. **Upload an image** of a German drink menu
2. **Choose extraction method**:
   - Basic OCR: Returns raw extracted text
   - AI-Enhanced: Returns formatted drink names and prices
3. **Receive results** in JSON format:
   ```json
   {
     "success": true,
     "result": "Becks - 4.50€\nKrombacher - 5.20€\nCoca Cola - 3.80€"
   }
   ```

## Development & Contribution

### Code Style

- Follow C# coding conventions
- Use meaningful variable and method names
- Add XML documentation for public APIs
- Implement proper error handling

### Testing

Run unit tests:
```bash
dotnet test
```

### Branching Strategy

- **main**: Production-ready code
- **develop**: Development branch
- **feature/***: Feature branches
- **hotfix/***: Critical fixes

### Commit Guidelines

- Use conventional commit messages
- Include issue numbers when applicable
- Keep commits focused and atomic

## Deployment

### Production Deployment

1. **Build for production**:
   ```bash
   dotnet publish -c Release -o ./publish
   ```

2. **Configure production settings**:
   - Update connection strings
   - Set production API keys
   - Configure logging levels

3. **Deploy to IIS/Azure/Docker** as needed

### Docker Support

Dockerfile is available in `PA.PAPA.API` directory for containerized deployment.

## Troubleshooting

### Common Issues

**OCR Not Working**:
- Ensure Tesseract data files are in `tessdata` folder
- Check image format (JPG, PNG, BMP supported)
- Verify image quality and resolution

**Database Connection Errors**:
- Verify connection string format
- Check database server accessibility
- Ensure proper authentication credentials

**OpenAI API Errors**:
- Validate API key configuration
- Check API quota and billing status
- Verify network connectivity

### Logging

Application logs are configured in `appsettings.json`:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

### Debug Mode

Enable detailed logging by setting:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug"
    }
  }
}
```

## License & References

- **License**: [Specify your license here]
- **Tesseract OCR**: https://github.com/tesseract-ocr/tesseract
- **OpenAI API**: https://platform.openai.com/docs
- **.NET Documentation**: https://docs.microsoft.com/en-us/dotnet/

## Support

For issues and questions:
- Create an issue in the repository
- Check existing documentation
- Review troubleshooting section above

---

**Version**: 1.0.0  
**Last Updated**: January 2024  
**Minimum .NET Version**: 9.0
