﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.DataAccess.Repositories.Admin.Interfaces
{
    public interface IServiceRepository : IDisposable
    {
        Task<IList<ServiceDTO>> Read();
        Task<ServiceDTO> Read(int id);
        Task<ServiceDTO> Create(ServiceDTO dto);
        Task<ServiceDTO> Update(ServiceDTO dto);
        Task<ServiceDTO> Delete(int id);
    }
}
