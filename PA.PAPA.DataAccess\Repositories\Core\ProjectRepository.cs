﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using PA.PAPA.DataAccess.Repositories.Core.Interfaces;
using PA.PAPA.DTO.Core;

namespace PA.PAPA.DataAccess.Repositories.Core
{
    public class ProjectRepository : IProjectRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public ProjectRepository(IConnectionFactory connectionFactory) {
            _connectionFactory = connectionFactory;
        }

        public async Task<ProjectDTO> Read(int id) {
            var param = new DynamicParameters();
            param.Add("@ProjectId", id);

            using (var results =
                await _connectionFactory.GetConnection.QueryMultipleAsync("Core.ProjectSelect", param,
                    commandType: CommandType.StoredProcedure)) {
                return (ProjectDTO)results.Read<ProjectDTO>().FirstOrDefault();
            }
        }

        public async Task<ProjectDTO> Create(ProjectDTO dto) {
            var param = new DynamicParameters();

            param.Add("@ProjectId", dto.ProjectId, null, ParameterDirection.InputOutput);
           

            param.Add("@UpdatedById", dto.UpdatedById);
            param.Add("@IsActive", dto.IsActive);

            using (var multiResult =
                await _connectionFactory.GetConnection.QueryMultipleAsync("Core.ProjectInsert", param,
                    commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();

                dto.ProjectId = param.Get<int>("@ProjectId");
            }

            return await Read(dto.ProjectId);
        }

        public async Task<ProjectDTO> Update(ProjectDTO dto) {
            var param = new DynamicParameters();

            param.Add("@ProjectId", dto.ProjectId);
           

            param.Add("@UpdatedById", dto.UpdatedById);
            param.Add("@IsActive", dto.IsActive);

            using (var multiResult = await _connectionFactory.GetConnection.QueryMultipleAsync(
                "Admin.ProjectUpdate", param, commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();
            }

            return await Read(dto.ProjectId);
        }




        public async Task<IEnumerable<ProjectDTO>> ReadEmployeeProjects(int employeeId) {
            var param = new DynamicParameters();
            param.Add("@EmployeeId", employeeId);
            return await _connectionFactory.GetConnection.QueryAsync<ProjectDTO>("Core.ProjectSelectByEmployee", param, commandType: CommandType.StoredProcedure);
        }
        
        public async Task<IEnumerable<ProjectDTO>> ReadProjects() {
            var param = new DynamicParameters();
            return await _connectionFactory.GetConnection.QueryAsync<ProjectDTO>("Core.ProjectSelect", param, commandType: CommandType.StoredProcedure);                
        }

        public async Task<IEnumerable<ProjectActivityDTO>> ReadProjectActivities() {
            var param = new DynamicParameters();
            return await _connectionFactory.GetConnection.QueryAsync<ProjectActivityDTO>("Core.ProjectActivitySelect", param, commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<ProjectEmployeeActivityDTO>> ReadEmployeeProjectActivities(int employeeId) {
            var param = new DynamicParameters();
            param.Add("@EmployeeId", employeeId);
            return await _connectionFactory.GetConnection.QueryAsync<ProjectEmployeeActivityDTO>("Core.ProjectActivitiySelectByEmployee", param, commandType: CommandType.StoredProcedure);
        }
        

        public void Dispose() {
            _connectionFactory.Dispose();
        }

    }
}