{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "C#: NT.BIRAOCR.API [https]",
            "type": "dotnet",
            "request": "launch",
            "projectPath": "${workspaceFolder}\\NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj",
            // For .NET Core 3.1, 5, 6, or 7 use:
            "launchBrowser": {
                "enabled": true,
                "launchUrl": "swagger",
                "args": "--no-sandbox"
            },
            "launchSettingsProfile": "https",
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            }
        }
    ]
}