{"format": 1, "restore": {"M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj": {}}, "projects": {"M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj", "projectName": "NT.BIRAOCR.API", "projectPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\NT.BIRAOCR.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"M:\\NugetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DataAccess\\NT.BIRAOCR.DataAccess.csproj": {"projectPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DataAccess\\NT.BIRAOCR.DataAccess.csproj"}, "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DTO\\NT.BIRAOCR.DTO.csproj": {"projectPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DTO\\NT.BIRAOCR.DTO.csproj"}, "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.Logic\\NT.BIRAOCR.Logic.csproj": {"projectPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.Logic\\NT.BIRAOCR.Logic.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.2, )"}, "MySql.Data": {"target": "Package", "version": "[9.3.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.1, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}, "Tesseract": {"target": "Package", "version": "[5.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DataAccess\\NT.BIRAOCR.DataAccess.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DataAccess\\NT.BIRAOCR.DataAccess.csproj", "projectName": "NT.BIRAOCR.DataAccess", "projectPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DataAccess\\NT.BIRAOCR.DataAccess.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DataAccess\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"M:\\NugetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DTO\\NT.BIRAOCR.DTO.csproj": {"projectPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DTO\\NT.BIRAOCR.DTO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DTO\\NT.BIRAOCR.DTO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DTO\\NT.BIRAOCR.DTO.csproj", "projectName": "NT.BIRAOCR.DTO", "projectPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DTO\\NT.BIRAOCR.DTO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DTO\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"M:\\NugetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.Logic\\NT.BIRAOCR.Logic.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.Logic\\NT.BIRAOCR.Logic.csproj", "projectName": "NT.BIRAOCR.Logic", "projectPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.Logic\\NT.BIRAOCR.Logic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.Logic\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"M:\\NugetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DataAccess\\NT.BIRAOCR.DataAccess.csproj": {"projectPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DataAccess\\NT.BIRAOCR.DataAccess.csproj"}, "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DTO\\NT.BIRAOCR.DTO.csproj": {"projectPath": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.DTO\\NT.BIRAOCR.DTO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}