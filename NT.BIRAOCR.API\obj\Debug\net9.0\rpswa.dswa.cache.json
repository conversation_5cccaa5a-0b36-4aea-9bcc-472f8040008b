{"GlobalPropertiesHash": "ZCZzXwV/TdaAoX9SDSvWLQ3ysTyKnm36SrXnovIQ4qk=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["2aky17yIl2HZmEbtbA6s9o9JtwE2RAFIMGzEU7myw4c=", "6VR3xe08KgFAAaOOmoDi7J3rVocOycJSbsAllV1iMGM=", "FJW0F91i+mKBVoJn1vPcmFIrAAHNwiij/eFDm8bJAO8=", "Oma+gUheN2SKzUF5K0ZSRtoUdDrgltk25sL8VDw4sj8=", "ADdblkhpif02dOs7AnxEHPbBifxe6EHYonLnmMeniBQ=", "5sHXG9EgWNhzqgMWsh02DmmL9He/y5CjKo0bYIF6rq4=", "LQR2CxFm/Br47jwgYtnWz7ku77+JIJBeydyylf1Ewy8=", "/QbuqnCWMaH2+rMyaW6jU7giZ0+yyLX4z3K7WQEH06U=", "re3AbGLv7FDE9nZ3Lee4ycEZqPWSSAwkEYED2JZBui8=", "aK3ee5kZnvP7gQuNdMKUSOXnQJ3y1E2X9K2gr5s3Pkw=", "lIB/mE/vio6fN1sR1D1D4zh5K9DO0b+JKYAEdz0WzyI=", "zQwGyrAfwgvEsJH4vdBRlB252weAbqPquH1Ko0X3lKY=", "Iod6EkmD2ckkzML5DiR2iBvSN2R9oGHIw5KukNK3wB0=", "pMBbI8Pd/r1AK/SRci+U9nfAkmg96JcVCMXsyaKa434=", "rNiJ3q5b72qQSZx+cv/d7q1u8hSDtIcNp9rqW32WTaI=", "mV/s8MKh71imw/XXL7Y3uWt+91fRD+xS/4GZsrlTpbI=", "qmvPghn4J7psS3HwMoGIhtlS/m0UqjibCiRPbc7jc7Y=", "UOxBFoJSKT+WIj8MGlD3gox9fOOqB8LAQvxXz8BhrkQ="], "CachedAssets": {"2aky17yIl2HZmEbtbA6s9o9JtwE2RAFIMGzEU7myw4c=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\beer-ocr-app.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "beer-ocr-app#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lq6hyijkef", "Integrity": "XVZ40lE0iKsCTg3ARqUIdPnPAGdcwuAWDS2KjPasVRU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\beer-ocr-app.html", "FileLength": 1173, "LastWriteTime": "2025-05-23T14:38:18+00:00"}, "6VR3xe08KgFAAaOOmoDi7J3rVocOycJSbsAllV1iMGM=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\buttonTest.js", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "buttonTest#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\buttonTest.js", "FileLength": 0, "LastWriteTime": "2025-05-26T19:55:56.64+00:00"}, "FJW0F91i+mKBVoJn1vPcmFIrAAHNwiij/eFDm8bJAO8=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\index.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4a7d5925mm", "Integrity": "5FAzzLLFg0fhmMwgw9ZWM+1KeTn6+tzvqdS/qog+r8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 8810, "LastWriteTime": "2025-05-20T00:49:42+00:00"}, "Oma+gUheN2SKzUF5K0ZSRtoUdDrgltk25sL8VDw4sj8=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "main#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bx607eef1i", "Integrity": "8d3F/ZWp3VK5gELlIDcbcHZhkaSuH+j5Gm/+eBRCazk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\main.html", "FileLength": 3160, "LastWriteTime": "2025-05-20T01:28:56+00:00"}, "ADdblkhpif02dOs7AnxEHPbBifxe6EHYonLnmMeniBQ=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\main.js", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "main#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "13ups3th7z", "Integrity": "cwNmIDCDRl++nWvg5dK/zqAKtiL3+IrQD2prGBHKS8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\main.js", "FileLength": 4265, "LastWriteTime": "2025-05-23T00:53:34+00:00"}, "5sHXG9EgWNhzqgMWsh02DmmL9He/y5CjKo0bYIF6rq4=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.css", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\mainForm.css", "FileLength": 0, "LastWriteTime": "2025-05-26T19:55:54.56+00:00"}, "LQR2CxFm/Br47jwgYtnWz7ku77+JIJBeydyylf1Ewy8=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lt4h0pu2ew", "Integrity": "qZY0JTxuQXt92bNXtMRU3BSEQMlWM3ppddHRDnFQWMw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\mainForm.html", "FileLength": 5897, "LastWriteTime": "2025-05-23T14:38:58+00:00"}, "/QbuqnCWMaH2+rMyaW6jU7giZ0+yyLX4z3K7WQEH06U=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.html.bak", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm.html#[.{fingerprint}]?.bak", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z6m5epkocb", "Integrity": "Ze6vaspCi2PrUT87dEqck+ymuqMKLx1xLIely4az75w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\mainForm.html.bak", "FileLength": 5887, "LastWriteTime": "2025-05-23T14:30:14+00:00"}, "re3AbGLv7FDE9nZ3Lee4ycEZqPWSSAwkEYED2JZBui8=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\mainForm.js", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "mainForm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\mainForm.js", "FileLength": 0, "LastWriteTime": "2025-05-26T19:55:56.22+00:00"}, "aK3ee5kZnvP7gQuNdMKUSOXnQJ3y1E2X9K2gr5s3Pkw=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.css", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "ocrtest#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jp5omh6saj", "Integrity": "50yZo1tOQ6R5X2kYj5Ar68Ej0667YUQm9GFz4s0gNvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ocrtest.css", "FileLength": 7773, "LastWriteTime": "2025-05-23T14:03:54+00:00"}, "lIB/mE/vio6fN1sR1D1D4zh5K9DO0b+JKYAEdz0WzyI=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "ocrtest#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zh1ztznphx", "Integrity": "qbTnMnalBOnE2W1TsxrIvwVdm5LXhZgqLLogkWw2Ma0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ocrtest.html", "FileLength": 5887, "LastWriteTime": "2025-05-23T14:38:18+00:00"}, "zQwGyrAfwgvEsJH4vdBRlB252weAbqPquH1Ko0X3lKY=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\ocrtest.js", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "ocrtest#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7swnv70qx5", "Integrity": "SX9S8WTXYwPXKxHfCb3FYi2s+QmyJgelGz6WicdrvM0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\ocrtest.js", "FileLength": 16388, "LastWriteTime": "2025-05-23T14:30:14+00:00"}, "Iod6EkmD2ckkzML5DiR2iBvSN2R9oGHIw5KukNK3wB0=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simple-upload-test.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "simple-upload-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "20rbgnipeh", "Integrity": "concm4aUvEYqN5Jl1jYTNaH0W6lu4Lfi/+clnDkPdT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\simple-upload-test.html", "FileLength": 2877, "LastWriteTime": "2025-05-23T14:30:14+00:00"}, "pMBbI8Pd/r1AK/SRci+U9nfAkmg96JcVCMXsyaKa434=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\simpleTest.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "simpleTest#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\simpleTest.html", "FileLength": 0, "LastWriteTime": "2025-05-26T19:55:56.94+00:00"}, "rNiJ3q5b72qQSZx+cv/d7q1u8hSDtIcNp9rqW32WTaI=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\styles.css", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "styles#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7ppuzqynnz", "Integrity": "m2i2JFgg2sFJ4fHh6Ks8LlB4b3G6eqc51IRHuntSAzg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\styles.css", "FileLength": 1599, "LastWriteTime": "2025-05-20T00:49:42+00:00"}, "mV/s8MKh71imw/XXL7Y3uWt+91fRD+xS/4GZsrlTpbI=": {"Identity": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\upload-test.html", "SourceId": "NT.BIRAOCR.API", "SourceType": "Discovered", "ContentRoot": "M:\\aOzan\\int\\MenuScan\\NT.BIRAOCR.API\\wwwroot\\", "BasePath": "_content/NT.BIRAOCR.API", "RelativePath": "upload-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\upload-test.html", "FileLength": 0, "LastWriteTime": "2025-05-26T19:55:57.21+00:00"}}, "CachedCopyCandidates": {}}