using System.Data.SqlClient;
using System.Threading.Tasks;
using Dapper;

namespace PAPA_API.Services
{
    public class UserService
    {
        private readonly IConfiguration _configuration;

        public UserService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task<User?> GetUserByIdAsync(int id)
        {
            using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
            await connection.OpenAsync();

            var query = "SELECT * FROM Users WHERE Id = @Id";
            var user = await connection.QueryFirstOrDefaultAsync<User>(query, new { Id = id });

            return user;
        }
    }
} 