﻿using System.Collections.Generic;

namespace PA.PAPA.DTO
{
    public interface IBaseResponse<T>
    {
        void Add(string message, MessageType type);
        void Add(Message message, T entity);
        void AddEntity(T entity);
        (List<Message>, List<T>) GetAll();
    }

    public class BaseResponse<T> : IBaseResponse<T>
    {
        public List<T> Entities = new List<T>();
        public List<Message> Messages = new List<Message>();

        public BaseResponse()
        {
        }

        public BaseResponse(string message, MessageType type)
        {
            Messages.Add(new Message {Text = message, Type = type});
        }

        public BaseResponse(string message, MessageType type, T entity) : this(message, type)
        {
            Entities.Add(entity);
        }

        public BaseResponse(List<Message> messages, List<T> entityList)
        {
            Messages = messages;
            Entities = entityList;
        }

        //public bool Success => !Messages.Exists(e =>
        //{
        //    return e.Type == MessageType.Error || e.Type == MessageType.ValidationError ||
        //           e.Type == MessageType.ConcurrencyError;
        //});

        public void Add(string message, MessageType type)
        {
            Messages.Add(new Message {Text = message, Type = type});
        }

        public void Add(Message message, T entity)
        {
            Messages.Add(message);
            Entities.Add(entity);
        }

        public void AddEntity(T entity)
        {
            Entities.Add(entity);
        }

        public (List<Message>, List<T>) GetAll()
        {
            return (Messages, Entities);
        }
    } //item1 Response, Item2 payload
}