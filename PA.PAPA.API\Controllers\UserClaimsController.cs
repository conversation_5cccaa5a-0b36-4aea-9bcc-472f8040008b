﻿using System.Collections.Generic;
using BooksApi.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PA.PAPA.API.Services;

namespace PA.PAPA.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserClaimsController : ControllerBase
    {
        private readonly UserClaimService _userClaimService;

        public UserClaimsController(UserClaimService userClaimService)
        {
            _userClaimService = userClaimService;
        }

        [HttpGet]
        public ActionResult<List<UserClaim>> Get() =>
            _userClaimService.Get();

        [AllowAnonymous]
        [HttpGet("{userId:int}")]
        public ActionResult<List<UserClaim>> Get(int userId) =>
            _userClaimService.Get(userId);

        [HttpGet("{id:length(24)}", Name = "GetUserClaim")]
        public ActionResult<UserClaim> Get(string id)
        {
            var userClaim = _userClaimService.Get(id);

            if (userClaim == null)
            {
                return NotFound();
            }

            return userClaim;
        }

        [HttpPost]
        public ActionResult<UserClaim> Create(UserClaim userClaim)
        {
            _userClaimService.Create(userClaim);

            return CreatedAtRoute("GetUserClaim", new { id = userClaim.Id.ToString() }, userClaim);
        }

        [HttpPut("{id:length(24)}")]
        public IActionResult Update(string id, UserClaim userClaimIn)
        {
            var userClaim = _userClaimService.Get(id);

            if (userClaim == null)
            {
                return NotFound();
            }

            _userClaimService.Update(id, userClaimIn);

            return NoContent();
        }

        [HttpDelete("{id:length(24)}")]
        public IActionResult Delete(string id)
        {
            var userClaim = _userClaimService.Get(id);

            if (userClaim == null)
            {
                return NotFound();
            }

            _userClaimService.Remove(userClaim.Id);

            return NoContent();
        }
    }
}
