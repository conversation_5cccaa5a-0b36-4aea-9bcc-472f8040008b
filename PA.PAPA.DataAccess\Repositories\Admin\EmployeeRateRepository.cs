﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.DataAccess.Repositories.Admin
{
    public class EmployeeRateRepository : IEmployeeRateRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public EmployeeRateRepository(IConnectionFactory connectionFactory) {
            _connectionFactory = connectionFactory;
        }

        public async Task<IEnumerable<EmployeeRateDTO>> ReadByEmployee(int employeeId) {
            var param = new DynamicParameters();
            param.Add("@EmployeeId", employeeId);
            using (var results = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeRateSelectByEmployee", param, commandType: CommandType.StoredProcedure)) {
                return results.Read<EmployeeRateDTO>().ToList();
            }
        }

        public async Task<EmployeeRateDTO> Read(int id) {
            var param = new DynamicParameters();

            // pass in the organization id if we have one otherwise pass in 0 for all records
            param.Add("@EmployeeRateId", id);

            using (var results = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeRateSelect", param, commandType: CommandType.StoredProcedure)) {
                return (EmployeeRateDTO)results.Read<EmployeeRateDTO>();
            }
        }

        public async Task<EmployeeRateDTO> Create(EmployeeRateDTO dto) {
            var param = new DynamicParameters();

            param.Add("@EmployeeRateId", dto.EmployeeRateId, null, ParameterDirection.InputOutput);
            param.Add("@EmployeeId", dto.EmployeeId);
            param.Add("@Rate", dto.Rate);
            param.Add("@StartDate", dto.StartDate);
            param.Add("@EndDate", dto.EndDate);

            using (var multiResult = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeRateInsert", param, commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();
                dto.EmployeeRateId = param.Get<int>("@EmployeeRateId");
            }

            return await Read(dto.EmployeeRateId);
        }

        public async Task<EmployeeRateDTO> Update(EmployeeRateDTO dto) {
            var param = new DynamicParameters();

            param.Add("@EmployeeRateId", dto.EmployeeRateId);
            param.Add("@EmployeeId", dto.EmployeeId);
            param.Add("@Rate", dto.Rate);
            param.Add("@StartDate", dto.StartDate);
            param.Add("@EndDate", dto.EndDate);

            using (var multiResult = await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.EmployeeProxyUpdate", param, commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();
            }

            return await Read(dto.EmployeeRateId);
        }

        public void Dispose() {
            _connectionFactory.Dispose();
        }
    }
}
