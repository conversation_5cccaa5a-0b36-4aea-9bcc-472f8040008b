2025-04-30 19:04:34.260 -07:00 [Error] Error getting Activitys: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Net9\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Admin.ActivityRepository.Read() in C:\Net9\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\ActivityRepository.cs:line 22
   at PA.PAPA.Logic.Admin.ActivityLogic.Read() in C:\Net9\PAPA_API\PA.PAPA.Logic\Admin\ActivityLogic.cs:line 21
   at PA.PAPA.API.Controllers.ActivityController.Get() in C:\Net9\PAPA_API\PA.PAPA.API\Controllers\ActivityController.cs:line 32
2025-04-30 19:51:18.862 -07:00 [Error] Error getting Activitys: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at Microsoft.Data.SqlClient.SqlConnection.PermissionDemand()
   at Microsoft.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open(SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Net9\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Admin.ActivityRepository.Read() in C:\Net9\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\ActivityRepository.cs:line 22
   at PA.PAPA.Logic.Admin.ActivityLogic.Read() in C:\Net9\PAPA_API\PA.PAPA.Logic\Admin\ActivityLogic.cs:line 21
   at PA.PAPA.API.Controllers.ActivityController.Get() in C:\Net9\PAPA_API\PA.PAPA.API\Controllers\ActivityController.cs:line 32
2025-04-30 19:51:46.913 -07:00 [Error] Error getting Employees: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at Microsoft.Data.SqlClient.SqlConnection.PermissionDemand()
   at Microsoft.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open(SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Net9\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read() in C:\Net9\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 24
   at PA.PAPA.Logic.Admin.EmployeeLogic.Read() in C:\Net9\PAPA_API\PA.PAPA.Logic\Admin\EmployeeLogic.cs:line 21
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Net9\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 31
