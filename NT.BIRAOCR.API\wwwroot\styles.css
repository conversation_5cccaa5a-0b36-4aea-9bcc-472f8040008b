/* styles.css - OsirTechAnalyzer API Test Interface */

body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 20px;
  color: #333;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
}

h1 {
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.api-section {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 20px;
  background-color: #f9f9f9;
}

h2 {
  color: #2980b9;
  margin-top: 0;
}

.endpoint {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 5px;
}

button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  border-radius: 3px;
  margin-top: 10px;
}

  button:hover {
    background-color: #2980b9;
  }

textarea, input {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 3px;
  box-sizing: border-box;
}

textarea {
  min-height: 100px;
  font-family: monospace;
}

.response {
  margin-top: 15px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 3px;
  white-space: pre-wrap;
  font-family: monospace;
  display: none;
  max-height: 300px;
  overflow: auto;
}

#token-display {
  margin: 15px 0;
  padding: 10px;
  background-color: #e8f4f8;
  border: 1px solid #bde0ec;
  border-radius: 3px;
  word-break: break-all;
}

.auth-required {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #c0392b;
}
