/*
 * © 2025 <PERSON> | SmartIT. All Rights Reserved.
 * https://johnkocer.github.io
 * Version: 1.0.0 Date: 05-21-2025
 */

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NT.BIRAOCR.Logic.Interfaces;

namespace NT.BIRAOCR.Logic.Services
{
    public class BlobStorageService : IBlobStorageService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<BlobStorageService> _logger;
        private readonly string _uploadDirectory;

        public BlobStorageService(IConfiguration configuration, ILogger<BlobStorageService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            
            // Get the upload directory from configuration or use a default
            _uploadDirectory = _configuration["Storage:UploadDirectory"] ?? Path.Combine(Directory.GetCurrentDirectory(), "uploads");
            
            // Ensure the directory exists
            if (!Directory.Exists(_uploadDirectory))
            {
                Directory.CreateDirectory(_uploadDirectory);
            }
        }

        public async Task<string> UploadFileAsync(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    throw new ArgumentException("File is empty or null");
                }

                // Generate a unique filename
                string fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
                string filePath = Path.Combine(_uploadDirectory, fileName);

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation($"File uploaded successfully: {filePath}");
                
                // Return the relative path
                return fileName;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file");
                throw;
            }
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                string fullPath = Path.Combine(_uploadDirectory, filePath);
                
                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);
                    _logger.LogInformation($"File deleted successfully: {fullPath}");
                    return true;
                }
                
                _logger.LogWarning($"File not found for deletion: {fullPath}");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting file: {filePath}");
                return false;
            }
        }

        public async Task<byte[]> DownloadFileAsync(string filePath)
        {
            try
            {
                string fullPath = Path.Combine(_uploadDirectory, filePath);
                
                if (!File.Exists(fullPath))
                {
                    _logger.LogWarning($"File not found for download: {fullPath}");
                    throw new FileNotFoundException($"File not found: {filePath}");
                }
                
                byte[] fileBytes = await File.ReadAllBytesAsync(fullPath);
                _logger.LogInformation($"File downloaded successfully: {fullPath}");
                
                return fileBytes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error downloading file: {filePath}");
                throw;
            }
        }
    }
}
