﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.DataAccess.Repositories.Admin
{
    public class ServiceRepository : IServiceRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public ServiceRepository(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        public async Task<IList<ServiceDTO>> Read()
        {
            var param = new DynamicParameters();

            using (var results =
                await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.ServiceSelect", param,
                    commandType: CommandType.StoredProcedure)) {
                return results.Read<ServiceDTO>().ToList();
            }
        }

        public async Task<ServiceDTO> Read(int id)
        {
            var param = new DynamicParameters();
            param.Add("@ServiceId", id);

            using (var results =
                await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.ServiceSelect", param,
                    commandType: CommandType.StoredProcedure)) {
                return (ServiceDTO)results.Read<ServiceDTO>().FirstOrDefault();
            }
        }

        public async Task<ServiceDTO> Create(ServiceDTO dto)
        {
            var param = new DynamicParameters();

            param.Add("@ServiceId", dto.ServiceId, null, ParameterDirection.InputOutput);
            param.Add("@Name", dto.Name);
            param.Add("@CreatedById", dto.CreatedById);
            param.Add("@IsActive", dto.IsActive);

            using (var multiResult =
                await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.ServiceInsert", param,
                    commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();

                dto.ServiceId = param.Get<int>("@ServiceId");
            }

            return await Read(dto.ServiceId);
        }

        public async Task<ServiceDTO> Update(ServiceDTO dto)
        {
            var param = new DynamicParameters();

            param.Add("@ServiceId", dto.ServiceId);
            param.Add("@Name", dto.Name);
            param.Add("@UpdatedById", dto.CreatedById);
            param.Add("@IsActive", dto.IsActive);

            using (var multiResult = await _connectionFactory.GetConnection.QueryMultipleAsync(
                "Admin.ServiceUpdate", param, commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();
            }

            return await Read(dto.ServiceId);
        }

        public async Task<ServiceDTO> Delete(int id)
        {
            var param = new DynamicParameters();
            param.Add("@ServiceId", id);

            using (var results =
                await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.ServiceDelete", param,
                    commandType: CommandType.StoredProcedure)) {
                return await results.ReadFirstOrDefaultAsync(); 
            }         
        }

        public void Dispose()
        {
            _connectionFactory.Dispose();
        }
    }
}
