<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Upload Button Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            text-align: center;
        }
        button {
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 20px;
            font-size: 16px;
        }
        #status {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            min-height: 50px;
        }
    </style>
</head>
<body>
    <h1>Simple Upload Button Test</h1>
    
    <button id="uploadBtn">Upload Photo</button>
    <input type="file" id="fileInput" accept=".jpg,.jpeg,.png,.bmp" style="display: none">
    
    <div id="status">Status: Ready</div>
    
    <script>
        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up event listeners');
            
            // Get references to elements
            const uploadBtn = document.getElementById('uploadBtn');
            const fileInput = document.getElementById('fileInput');
            const status = document.getElementById('status');
            
            // Log element existence
            console.log('Upload button found:', !!uploadBtn);
            console.log('File input found:', !!fileInput);
            
            // Add click event listener to button
            uploadBtn.addEventListener('click', function() {
                console.log('Button clicked');
                status.textContent = 'Button clicked at ' + new Date().toLocaleTimeString();
                
                // Trigger file input click
                if (fileInput) {
                    fileInput.click();
                } else {
                    status.textContent = 'Error: File input not found';
                }
            });
            
            // Add change event listener to file input
            fileInput.addEventListener('change', function(event) {
                const file = event.target.files[0];
                console.log('File selected:', file ? file.name : 'none');
                
                if (file) {
                    status.textContent = `File selected: ${file.name}`;
                } else {
                    status.textContent = 'No file selected';
                }
            });
            
            // Log that setup is complete
            console.log('Event listeners set up');
        });
    </script>
</body>
</html>
