﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

namespace BooksApi.Models
{
    public class UserClaim
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        public int ClaimId { get; set; }

        public int UserId { get; set; }

        [BsonElement("ClaimType")]
        [JsonProperty("ClaimType")]
        public string ClaimType { get; set; }

        [BsonElement("ClaimValue")]
        [JsonProperty("ClaimValue")]
        public string ClaimValue { get; set; }

        public string EmployeeNumber { get; set; }
    }
}