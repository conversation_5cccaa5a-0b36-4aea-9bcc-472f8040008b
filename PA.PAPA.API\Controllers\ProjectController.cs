﻿using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PA.PAPA.DataAccess.Repositories.Core.Interfaces;
using PA.PAPA.DTO;
using PA.PAPA.DTO.Core;
using PA.PAPA.DTO.Logging;
using PA.PAPA.Logic.Core.Interfaces;
using SmartIT.DebugHelper;
using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace PA.PAPA.API.Controllers
{
    [Route("api/[controller]")]
    //[Authorize]
    public class ProjectController : Controller
    {
        private readonly ILogger<ProjectController> _logger;
        private readonly IProjectLogic _logic;

        public ProjectController(IProjectLogic ProjectLogic, ILogger<ProjectController> logger) {
            _logic = ProjectLogic;
            _logger = logger;           
        }

        [HttpGet("{id:int}")]
        public async Task<IActionResult> Get(int id) {
            try {
                var item = await _logic.Read(id);
                if (item == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetById200, item.ConvertToJson());
                return Ok(item);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new ErrorResponse {
                        Messages = new List<Message>
                        {
                            new Message
                            {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(ex, $"Error getting Project Ids: {id.ConvertToJson()}", request);

                return request;
            }
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody] ProjectDTO dto) {
            if (dto == null) {
                var ErrorResponse = new ErrorResponse("Empty Payload", MessageType.Error);
                return BadRequest(ErrorResponse);
            }

            try {
                var entity = await _logic.Create(dto);
                _logger.LogInformation(Events.Post, null, Messages.PostRecordCreated_201, entity.ConvertToJson());

                return Ok(entity);
            }
            catch (ValidationException ex) {
                var request = BadRequest(new ErrorResponse(ex.Errors.Select(a => (PropertyValidationError)a)));
                _logger.LogError(Events.Post, ex, "Error saving Projects: ", request.ConvertToJson());
                return request;
            }

            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    // Violation in one on both...
                    var ErrorResponse = new ErrorResponse("Project already exists", MessageType.Error);
                    ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                    _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson(), ErrorResponse.ConvertToJson());

                    return BadRequest(ErrorResponse);
                }

                var error = new ErrorResponse("An error has occurred processing your request.", MessageType.Error);
                error.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson(), error.ConvertToJson());

                return BadRequest(error);
            }

            catch (Exception ex) {
                var ErrorResponse = new ErrorResponse(
                    "An error has occurred processing your request.",
                    MessageType.Error);
                ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson());
                return BadRequest(ErrorResponse);
            }
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] ProjectDTO dto) {
            if (dto == null) {
                var ErrorResponse = new ErrorResponse("Empty Payload", MessageType.Error);
                return BadRequest(ErrorResponse);
            }

            try {
                var entity = await _logic.Update(dto);
                _logger.LogInformation(Events.Put, null, Messages.PutRecordUpdated, entity.ConvertToJson());

                return Ok(entity);
            }
            catch (ValidationException ex) {
                var request = BadRequest(new ErrorResponse(ex.Errors.Select(a => (PropertyValidationError)a)));
                _logger.LogError(Events.Put, ex, "Error saving Projects: ", request.ConvertToJson());
                return request;
            }

            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    // Violation in one on both...
                    var ErrorResponse = new ErrorResponse("Project already exists", MessageType.Error);
                    ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                    _logger.LogError(Events.Put, ex, Messages.PutRecordUpdateError, dto.ConvertToJson(), ErrorResponse.ConvertToJson());

                    return BadRequest(ErrorResponse);
                }

                var error = new ErrorResponse("An error has occurred processing your request.", MessageType.Error);
                error.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Put, ex, Messages.PutRecordUpdateError, dto.ConvertToJson(), error.ConvertToJson());

                return BadRequest(error);
            }

            catch (Exception ex) {
                var ErrorResponse = new ErrorResponse(
                    "An error has occurred processing your request.",
                    MessageType.Error);
                ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Put, ex, Messages.PutRecordUpdateError, dto.ConvertToJson());
                return BadRequest(ErrorResponse);
            }
        }

        [HttpGet]        
        public async Task<IActionResult> GetProjects() {
            try {
                var list = await _logic.ReadProjects();
                if (list == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetListNumberOfRecordReturned200, list.Count());
                return Ok(list);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new ErrorResponse {
                        Messages = new List<Message>
                        {
                            new Message
                            {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(Events.Get, ex, "Error getting Projects: ", request.ConvertToJson());
                return request;
            }
        }

        [HttpGet]
        [Route("activities")]
        public async Task<IActionResult> GetProjectActivities() {
            try {
                var list = await _logic.ReadProjectActivities();
                if (list == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetListNumberOfRecordReturned200, list.Count());
                return Ok(list);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new ErrorResponse {
                        Messages = new List<Message>
                        {
                            new Message
                            {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(Events.Get, ex, "Error getting Projects: ", request.ConvertToJson());
                return request;
            }
        }

        [HttpGet]
        [Route("activities/employee/{employeeId:int}")]
        public async Task<IActionResult> GetEmployeeProjectActivities(int employeeId) {
            try {
                var list = await _logic.ReadEmployeeProjectActivities(employeeId);
                if (list == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetListNumberOfRecordReturned200, list.Count());
                return Ok(list);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new ErrorResponse {
                        Messages = new List<Message>
                        {
                            new Message
                            {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(Events.Get, ex, "Error getting Projects: ", request.ConvertToJson());
                return request;
            }
        }

        [HttpGet]
        [Route("employee/{employeeId:int}")]
        public async Task<IActionResult> GetEmployeeProjects(int employeeId) {
            try {
                var list = await _logic.ReadEmployeeProjects(employeeId);
                if (list == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetListNumberOfRecordReturned200, list.Count());
                return Ok(list);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new ErrorResponse {
                        Messages = new List<Message>
                        {
                            new Message
                            {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(Events.Get, ex, "Error getting Projects: ", request.ConvertToJson());
                return request;
            }
        }

        protected override void Dispose(bool disposing) {
            if (disposing) {
                _logic.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}