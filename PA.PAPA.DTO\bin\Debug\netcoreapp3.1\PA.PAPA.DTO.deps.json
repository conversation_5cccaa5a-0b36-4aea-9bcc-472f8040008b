{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {"PA.PAPA.DTO/1.0.0": {"dependencies": {"FluentValidation.AspNetCore": "9.0.1"}, "runtime": {"PA.PAPA.DTO.dll": {}}}, "FluentValidation/9.0.1": {"runtime": {"lib/netstandard2.0/FluentValidation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FluentValidation.AspNetCore/9.0.1": {"dependencies": {"FluentValidation": "9.0.1", "FluentValidation.DependencyInjectionExtensions": "9.0.1"}, "runtime": {"lib/netcoreapp3.1/FluentValidation.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FluentValidation.DependencyInjectionExtensions/9.0.1": {"dependencies": {"FluentValidation": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0"}, "runtime": {"lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.0": {}}}, "libraries": {"PA.PAPA.DTO/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FluentValidation/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GeF+cPknUFFJ5FkCybmQShaXWwJowBuJozgns6In37wiP2zOWx+kdNPBAN9BlDyhcORdxtXeGnkLZ+OFT85onQ==", "path": "fluentvalidation/9.0.1", "hashPath": "fluentvalidation.9.0.1.nupkg.sha512"}, "FluentValidation.AspNetCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7DlvStNVZwSAbLC5GyT3nGa8K11hb9R+W6BgnYnSjvnPFGWKpyANwuGTl8HShOInjXJqIJykqHo1UcdemTq/7g==", "path": "fluentvalidation.aspnetcore/9.0.1", "hashPath": "fluentvalidation.aspnetcore.9.0.1.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-h71E7XXjFJZsIs6s7AJz2uF4g2BTmEJk3mTKviVRtXuAUF9yV3B7glZjIpEncV6i4u7caqFixKeVpSBB3jVL9w==", "path": "fluentvalidation.dependencyinjectionextensions/9.0.1", "hashPath": "fluentvalidation.dependencyinjectionextensions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-8/CtASu80UIoyG+r8FstrmZW5GLtXxzoYpjj3jV0FKZCL5CiFgSH3pAmqut/dC68mu7N1bU6v0UtKKL3gCUQGQ==", "path": "microsoft.extensions.dependencyinjection.abstractions/2.1.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.2.1.0.nupkg.sha512"}}}