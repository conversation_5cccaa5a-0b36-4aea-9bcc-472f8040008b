﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.DataAccess.Repositories.Admin
{
    public class ActivityRepository : IActivityRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public ActivityRepository(IConnectionFactory connectionFactory) {
            _connectionFactory = connectionFactory;
        }

        public async Task<IList<ActivityDTO>> Read() {
            var param = new DynamicParameters();

            using (var results =
                await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.ActivitySelect", param,
                    commandType: CommandType.StoredProcedure)) {
                return results.Read<ActivityDTO>().ToList();
            }
        }

        public async Task<ActivityDTO> Read(int id) {
            var param = new DynamicParameters();
            param.Add("@ActivityId", id);

            using (var results =
                await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.ActivitySelect", param,
                    commandType: CommandType.StoredProcedure)) {
                return (ActivityDTO)results.Read<ActivityDTO>().FirstOrDefault();
            }
        }

        public async Task<ActivityDTO> Create(ActivityDTO dto) {
            var param = new DynamicParameters();

            param.Add("@ActivityId", dto.ActivityId, null, ParameterDirection.InputOutput);
            param.Add("@Name", dto.Name);
            param.Add("@Code", dto.Code);
            param.Add("@IsCapEx", dto.IsCapEx);
            param.Add("@UpdatedById", dto.UpdatedById);
            param.Add("@IsActive", dto.IsActive);

            using (var multiResult =
                await _connectionFactory.GetConnection.QueryMultipleAsync("Admin.ActivityInsert", param,
                    commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();

                dto.ActivityId = param.Get<int>("@ActivityId");
            }

            return await Read(dto.ActivityId);
        }

        public async Task<ActivityDTO> Update(ActivityDTO dto) {
            var param = new DynamicParameters();

            param.Add("@ActivityId", dto.ActivityId);
            param.Add("@Name", dto.Name);
            param.Add("@Code", dto.Code);
            param.Add("@IsCapEx", dto.IsCapEx);
            param.Add("@UpdatedById", dto.UpdatedById);
            param.Add("@IsActive", dto.IsActive);

            using (var multiResult = await _connectionFactory.GetConnection.QueryMultipleAsync(
                "Admin.ActivityUpdate", param, commandType: CommandType.StoredProcedure)) {
                await multiResult.ReadFirstOrDefaultAsync<int>();
            }

            return await Read(dto.ActivityId);
        }

        public void Dispose() {
            _connectionFactory.Dispose();
        }

    }
}