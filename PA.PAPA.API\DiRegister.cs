using Microsoft.Extensions.DependencyInjection;
using PA.PAPA.Admin.Logic;
using PA.PAPA.DataAccess;
using PA.PAPA.DataAccess.Repositories.Admin;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DataAccess.Repositories.Core;
using PA.PAPA.DataAccess.Repositories.Core.Interfaces;
using PA.PAPA.DataAccess.Services;
using PA.PAPA.Logic.Admin;
using PA.PAPA.Logic.Admin.Interfaces;
using PA.PAPA.Logic.Core;
using PA.PAPA.Logic.Core.Interfaces;

namespace PA.PAPA.API
{
    public class DiRegister
    {
        internal static void InitDiRegister(IServiceCollection services)
        {
            #region DIRegister

            services.AddScoped<IActivityRepository, ActivityRepository>();
            services.AddScoped<IEmployeeProxyRepository, EmployeeProxyRepository>();
            services.AddScoped<IEmployeeRateRepository, EmployeeRateRepository>();
            services.AddScoped<IEmployeeRepository, EmployeeRepository>();
            services.AddScoped<IProjectRepository, ProjectRepository>();
            services.AddScoped<IServiceRepository, ServiceRepository>();
            services.AddScoped<ITimesheetRepository, TimesheetRepository>();
            services.AddTransient<IConnectionFactory, ConnectionFactory>();
            services.AddTransient<IHttpClientService, HttpClientService>();

            #endregion

            #region Logic Layer

            services.AddScoped<IActivityLogic, ActivityLogic>();
            services.AddScoped<IEmployeeLogic, EmployeeLogic>();
            services.AddScoped<IEmployeeProxyLogic, EmployeeProxyLogic>();
            services.AddScoped<IEmployeeRateLogic, EmployeeRateLogic>();
            services.AddScoped<IProjectLogic, ProjectLogic>();
            services.AddScoped<IServiceLogic, ServiceLogic>();
            services.AddScoped<ITimesheetLogic, TimesheetLogic>();

            #endregion
        }
    }
}