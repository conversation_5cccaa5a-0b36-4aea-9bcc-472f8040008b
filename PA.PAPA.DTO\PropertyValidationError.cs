﻿using System;
using System.Collections.Generic;
using System.Text;

namespace PA.PAPA.DTO
{
    public class PropertyValidationError
    {
        public string PropertyName { get; set; }
        public string ErrorMessage { get; set; }
        public int Severity { get; set; }


        public static explicit operator PropertyValidationError(FluentValidation.Results.ValidationFailure failure) {
            return new PropertyValidationError() {
                PropertyName = failure.PropertyName,
                ErrorMessage = failure.ErrorMessage,
                Severity = (int)failure.Severity
            };
        }

    }
}
