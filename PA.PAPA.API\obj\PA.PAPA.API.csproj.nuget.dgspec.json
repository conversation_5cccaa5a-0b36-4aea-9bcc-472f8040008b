{"format": 1, "restore": {"C:\\Net9\\PAPA_API\\PA.PAPA.API\\PA.PAPA.API.csproj": {}}, "projects": {"C:\\Net9\\PAPA_API\\PA.PAPA.API\\PA.PAPA.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Net9\\PAPA_API\\PA.PAPA.API\\PA.PAPA.API.csproj", "projectName": "PA.PAPA.API", "projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.API\\PA.PAPA.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Net9\\PAPA_API\\PA.PAPA.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"M:\\NugetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Net9\\PAPA_API\\PA.PAPA.DataAccess\\PA.PAPA.DataAccess.csproj": {"projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DataAccess\\PA.PAPA.DataAccess.csproj"}, "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj": {"projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj"}, "C:\\Net9\\PAPA_API\\PA.PAPA.Logic\\PA.PAPA.Logic.csproj": {"projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.Logic\\PA.PAPA.Logic.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.5, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.19.5, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.24.0, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[2.3.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[1.5.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.RollingFile": {"target": "Package", "version": "[3.3.0, )"}, "Serilog.Sinks.Seq": {"target": "Package", "version": "[6.0.0, )"}, "SmartIT.DebugTraceHelper": {"target": "Package", "version": "[1.0.5, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Net9\\PAPA_API\\PA.PAPA.DataAccess\\PA.PAPA.DataAccess.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Net9\\PAPA_API\\PA.PAPA.DataAccess\\PA.PAPA.DataAccess.csproj", "projectName": "PA.PAPA.DataAccess", "projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DataAccess\\PA.PAPA.DataAccess.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DataAccess\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"M:\\NugetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj": {"projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.28, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.5, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj", "projectName": "PA.PAPA.DTO", "projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"M:\\NugetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Net9\\PAPA_API\\PA.PAPA.Logic\\PA.PAPA.Logic.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Net9\\PAPA_API\\PA.PAPA.Logic\\PA.PAPA.Logic.csproj", "projectName": "PA.PAPA.Logic", "projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.Logic\\PA.PAPA.Logic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Net9\\PAPA_API\\PA.PAPA.Logic\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"M:\\NugetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Net9\\PAPA_API\\PA.PAPA.DataAccess\\PA.PAPA.DataAccess.csproj": {"projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DataAccess\\PA.PAPA.DataAccess.csproj"}, "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj": {"projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}}}