{"version": 3, "targets": {"net9.0": {"AWSSDK.Core/3.7.100.14": {"type": "package", "compile": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.SecurityToken/3.7.100.14": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.100.14, 4.0.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.SecurityToken.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SecurityToken.dll": {"related": ".pdb;.xml"}}}, "Azure.Core/1.35.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.10.3": {"type": "package", "dependencies": {"Azure.Core": "1.35.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "4.56.0", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}}, "Dapper/2.1.28": {"type": "package", "compile": {"lib/net7.0/Dapper.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Dapper.dll": {"related": ".xml"}}}, "DnsClient/1.6.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "compile": {"lib/net5.0/DnsClient.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/DnsClient.dll": {"related": ".xml"}}}, "FluentValidation/11.5.1": {"type": "package", "compile": {"lib/net7.0/FluentValidation.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/FluentValidation.dll": {"related": ".xml"}}}, "FluentValidation.AspNetCore/11.3.0": {"type": "package", "dependencies": {"FluentValidation": "11.5.1", "FluentValidation.DependencyInjectionExtensions": "11.5.1"}, "compile": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"type": "package", "dependencies": {"FluentValidation": "11.5.1", "Microsoft.Extensions.Dependencyinjection.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.JsonPatch/8.0.2": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.2": {"type": "package", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "8.0.2", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "compile": {"ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Data.SqlClient/5.1.5": {"type": "package", "dependencies": {"Azure.Identity": "1.10.3", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.1", "Microsoft.Identity.Client": "4.56.0", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}, "compile": {"ref/net6.0/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"type": "package", "runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "build": {"build/Microsoft.Extensions.ApiDescription.Server.props": {}, "build/Microsoft.Extensions.ApiDescription.Server.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props": {}, "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Identity.Client/4.56.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.22.0"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.56.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/1.0.1": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.0.1": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.OpenApi/1.2.3": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.19.5": {"type": "package", "build": {"build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props": {}, "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets": {}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "MongoDB.Bson/2.24.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/netstandard2.1/MongoDB.Bson.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"related": ".xml"}}}, "MongoDB.Driver/2.24.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.0.0", "MongoDB.Bson": "2.24.0", "MongoDB.Driver.Core": "2.24.0", "MongoDB.Libmongocrypt": "1.8.2"}, "compile": {"lib/netstandard2.1/MongoDB.Driver.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"related": ".xml"}}}, "MongoDB.Driver.Core/2.24.0": {"type": "package", "dependencies": {"AWSSDK.SecurityToken": "3.7.100.14", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "MongoDB.Bson": "2.24.0", "MongoDB.Libmongocrypt": "1.8.2", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "compile": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"related": ".xml"}}}, "MongoDB.Libmongocrypt/1.8.2": {"type": "package", "compile": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {}}, "runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}, "build": {"build/_._": {}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"assetType": "native", "rid": "linux"}, "runtimes/osx/native/libmongocrypt.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win/native/mongocrypt.dll": {"assetType": "native", "rid": "win"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.1"}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"related": ".pdb;.xml"}}}, "Serilog/4.2.0": {"type": "package", "compile": {"lib/net9.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.dll": {"related": ".xml"}}}, "Serilog.AspNetCore/9.0.0": {"type": "package", "dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "compile": {"lib/net9.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Serilog.Enrichers.Environment/2.3.0": {"type": "package", "dependencies": {"Serilog": "2.3.0"}, "compile": {"lib/netstandard2.0/Serilog.Enrichers.Environment.dll": {}}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.Environment.dll": {}}}, "Serilog.Extensions.Hosting/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}, "compile": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}}, "Serilog.Extensions.Logging/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "9.0.0", "Serilog": "4.2.0"}, "compile": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.0"}, "compile": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}}, "Serilog.Sinks.Async/1.5.0": {"type": "package", "dependencies": {"Serilog": "2.9.0"}, "compile": {"lib/netstandard2.0/Serilog.Sinks.Async.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Sinks.Async.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/6.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"type": "package", "dependencies": {"Serilog": "2.0.0"}, "compile": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"related": ".xml"}}}, "Serilog.Sinks.RollingFile/3.3.0": {"type": "package", "dependencies": {"Serilog.Sinks.File": "3.2.0", "System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding.Extensions": "4.0.11"}, "compile": {"lib/netstandard1.3/Serilog.Sinks.RollingFile.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/Serilog.Sinks.RollingFile.dll": {"related": ".xml"}}}, "Serilog.Sinks.Seq/6.0.0": {"type": "package", "dependencies": {"Serilog": "3.1.1", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Sinks.File": "5.0.0", "Serilog.Sinks.PeriodicBatching": "3.1.0"}, "compile": {"lib/net6.0/Serilog.Sinks.Seq.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Serilog.Sinks.Seq.dll": {"related": ".xml"}}}, "SharpCompress/0.30.1": {"type": "package", "compile": {"lib/net5.0/SharpCompress.dll": {}}, "runtime": {"lib/net5.0/SharpCompress.dll": {}}}, "SmartIT.DebugTraceHelper/1.0.5": {"type": "package", "dependencies": {"Newtonsoft.Json": "10.0.3"}, "compile": {"lib/netcoreapp2.0/SmartIT.DebugHelper.dll": {}}, "runtime": {"lib/netcoreapp2.0/SmartIT.DebugHelper.dll": {}}}, "Snappier/1.0.0": {"type": "package", "compile": {"lib/net5.0/Snappier.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Snappier.dll": {"related": ".xml"}}}, "Swashbuckle.AspNetCore/6.5.0": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}, "build": {"build/Swashbuckle.AspNetCore.props": {}}}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.2.3"}, "compile": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.5.0"}, "compile": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"type": "package", "compile": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Drawing.Common/6.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Formats.Asn1/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Formats.Asn1.dll": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "compile": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.IO.FileSystem.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.FileSystem.Primitives/4.0.1": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Memory.Data/1.0.2": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reflection/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Runtime/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Caching/6.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Runtime.Handles/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.5/System.Runtime.InteropServices.dll": {"related": ".xml"}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "dependencies": {"System.Formats.Asn1": "5.0.0"}, "compile": {"ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding.Extensions/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.Extensions.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/4.7.2": {"type": "package", "compile": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading.Tasks/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Windows.Extensions/6.0.0": {"type": "package", "dependencies": {"System.Drawing.Common": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "ZstdSharp.Port/0.7.3": {"type": "package", "compile": {"lib/net7.0/ZstdSharp.dll": {}}, "runtime": {"lib/net7.0/ZstdSharp.dll": {}}}, "PA.PAPA.DataAccess/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"Dapper": "2.1.28", "Microsoft.Data.SqlClient": "5.1.5", "Microsoft.Extensions.Options": "8.0.2", "PA.PAPA.DTO": "1.0.0"}, "compile": {"bin/placeholder/PA.PAPA.DataAccess.dll": {}}, "runtime": {"bin/placeholder/PA.PAPA.DataAccess.dll": {}}}, "PA.PAPA.DTO/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"FluentValidation.AspNetCore": "11.3.0"}, "compile": {"bin/placeholder/PA.PAPA.DTO.dll": {}}, "runtime": {"bin/placeholder/PA.PAPA.DTO.dll": {}}}, "PA.PAPA.Logic/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"FluentValidation.AspNetCore": "11.3.0", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.2", "PA.PAPA.DTO": "1.0.0", "PA.PAPA.DataAccess": "1.0.0"}, "compile": {"bin/placeholder/PA.PAPA.Logic.dll": {}}, "runtime": {"bin/placeholder/PA.PAPA.Logic.dll": {}}}}}, "libraries": {"AWSSDK.Core/3.7.100.14": {"sha512": "gnEgxBlk4PFEfdPE8Lkf4+D16MZFYSaW7/o6Wwe5e035QWUkTJX0Dn4LfTCdV5QSEL/fOFxu+yCAm55eIIBgog==", "type": "package", "path": "awssdk.core/3.7.100.14", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "awssdk.core.3.7.100.14.nupkg.sha512", "awssdk.core.nuspec", "lib/net35/AWSSDK.Core.dll", "lib/net35/AWSSDK.Core.pdb", "lib/net35/AWSSDK.Core.xml", "lib/net45/AWSSDK.Core.dll", "lib/net45/AWSSDK.Core.pdb", "lib/net45/AWSSDK.Core.xml", "lib/netcoreapp3.1/AWSSDK.Core.dll", "lib/netcoreapp3.1/AWSSDK.Core.pdb", "lib/netcoreapp3.1/AWSSDK.Core.xml", "lib/netstandard2.0/AWSSDK.Core.dll", "lib/netstandard2.0/AWSSDK.Core.pdb", "lib/netstandard2.0/AWSSDK.Core.xml", "tools/account-management.ps1"]}, "AWSSDK.SecurityToken/3.7.100.14": {"sha512": "dGCVuVo0CFUKWW85W8YENO+aREf8sCBDjvGbnNvxJuNW4Ss+brEU9ltHhq2KfZze2VUNK1/wygbPG1bmbpyXEw==", "type": "package", "path": "awssdk.securitytoken/3.7.100.14", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.SecurityToken.CodeAnalysis.dll", "awssdk.securitytoken.3.7.100.14.nupkg.sha512", "awssdk.securitytoken.nuspec", "lib/net35/AWSSDK.SecurityToken.dll", "lib/net35/AWSSDK.SecurityToken.pdb", "lib/net35/AWSSDK.SecurityToken.xml", "lib/net45/AWSSDK.SecurityToken.dll", "lib/net45/AWSSDK.SecurityToken.pdb", "lib/net45/AWSSDK.SecurityToken.xml", "lib/netcoreapp3.1/AWSSDK.SecurityToken.dll", "lib/netcoreapp3.1/AWSSDK.SecurityToken.pdb", "lib/netcoreapp3.1/AWSSDK.SecurityToken.xml", "lib/netstandard2.0/AWSSDK.SecurityToken.dll", "lib/netstandard2.0/AWSSDK.SecurityToken.pdb", "lib/netstandard2.0/AWSSDK.SecurityToken.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "Azure.Core/1.35.0": {"sha512": "hENcx03Jyuqv05F4RBEPbxz29UrM3Nbhnr6Wl6NQpoU9BCIbL3XLentrxDCTrH54NLS11Exxi/o8MYgT/cnKFA==", "type": "package", "path": "azure.core/1.35.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.35.0.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net5.0/Azure.Core.dll", "lib/net5.0/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netcoreapp2.1/Azure.Core.dll", "lib/netcoreapp2.1/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.10.3": {"sha512": "l1Xm2MWOF2Mzcwuarlw8kWQXLZk3UeB55aQXVyjj23aBfDwOZ3gu5GP2kJ6KlmZeZv2TCzw7x4L3V36iNr3gww==", "type": "package", "path": "azure.identity/1.10.3", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.10.3.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "Dapper/2.1.28": {"sha512": "ha49pzOEDmCPkMxwfPSR/wxa/6RD3r42TESIgpzpi7FXq/gNVUuJVEO+wtUzntNRhtmq3BKCl0s0aAlSZLkBUA==", "type": "package", "path": "dapper/2.1.28", "files": [".nupkg.metadata", ".signature.p7s", "Dapper.png", "dapper.2.1.28.nupkg.sha512", "dapper.nuspec", "lib/net461/Dapper.dll", "lib/net461/Dapper.xml", "lib/net5.0/Dapper.dll", "lib/net5.0/Dapper.xml", "lib/net7.0/Dapper.dll", "lib/net7.0/Dapper.xml", "lib/netstandard2.0/Dapper.dll", "lib/netstandard2.0/Dapper.xml", "readme.md"]}, "DnsClient/1.6.1": {"sha512": "4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "type": "package", "path": "dnsclient/1.6.1", "files": [".nupkg.metadata", ".signature.p7s", "dnsclient.1.6.1.nupkg.sha512", "dnsclient.nuspec", "icon.png", "lib/net45/DnsClient.dll", "lib/net45/DnsClient.xml", "lib/net471/DnsClient.dll", "lib/net471/DnsClient.xml", "lib/net5.0/DnsClient.dll", "lib/net5.0/DnsClient.xml", "lib/netstandard1.3/DnsClient.dll", "lib/netstandard1.3/DnsClient.xml", "lib/netstandard2.0/DnsClient.dll", "lib/netstandard2.0/DnsClient.xml", "lib/netstandard2.1/DnsClient.dll", "lib/netstandard2.1/DnsClient.xml"]}, "FluentValidation/11.5.1": {"sha512": "0h1Q5lNOLLyYTWMJmyNoMqhY4CBRvvUWvJP1R4F2CnmmzuWwvB0A8aVmw5+lOuwYnwUwCRrdeMLbc81F38ahNQ==", "type": "package", "path": "fluentvalidation/11.5.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.11.5.1.nupkg.sha512", "fluentvalidation.nuspec", "lib/net5.0/FluentValidation.dll", "lib/net5.0/FluentValidation.xml", "lib/net6.0/FluentValidation.dll", "lib/net6.0/FluentValidation.xml", "lib/net7.0/FluentValidation.dll", "lib/net7.0/FluentValidation.xml", "lib/netstandard2.0/FluentValidation.dll", "lib/netstandard2.0/FluentValidation.xml", "lib/netstandard2.1/FluentValidation.dll", "lib/netstandard2.1/FluentValidation.xml"]}, "FluentValidation.AspNetCore/11.3.0": {"sha512": "jtFVgKnDFySyBlPS8bZbTKEEwJZnn11rXXJ2SQnjDhZ56rQqybBg9Joq4crRLz3y0QR8WoOq4iE4piV81w/Djg==", "type": "package", "path": "fluentvalidation.aspnetcore/11.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.aspnetcore.11.3.0.nupkg.sha512", "fluentvalidation.aspnetcore.nuspec", "lib/net5.0/FluentValidation.AspNetCore.dll", "lib/net5.0/FluentValidation.AspNetCore.xml", "lib/net6.0/FluentValidation.AspNetCore.dll", "lib/net6.0/FluentValidation.AspNetCore.xml", "lib/netcoreapp3.1/FluentValidation.AspNetCore.dll", "lib/netcoreapp3.1/FluentValidation.AspNetCore.xml"]}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"sha512": "iWM0LS1MDYX06pcjMEQKqHirl2zkjHlNV23mEJSoR1IZI7KQmTa0RcTtGEJpj5+iHvBCfrzP2mYKM4FtRKVb+A==", "type": "package", "path": "fluentvalidation.dependencyinjectionextensions/11.5.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.dependencyinjectionextensions.11.5.1.nupkg.sha512", "fluentvalidation.dependencyinjectionextensions.nuspec", "lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.dll", "lib/netstandard2.0/FluentValidation.DependencyInjectionExtensions.xml", "lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll", "lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.xml"]}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.2": {"sha512": "7qJkk5k5jabATZZrMIQgpUB9yjDNAAApSqw+8d0FEyK1AJ4j+wv1qOMl2byUr837xbK+MjehtPnQ32yZ5Gtzlw==", "type": "package", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll", "lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.xml", "microsoft.aspnetcore.authentication.jwtbearer.8.0.2.nupkg.sha512", "microsoft.aspnetcore.authentication.jwtbearer.nuspec"]}, "Microsoft.AspNetCore.JsonPatch/8.0.2": {"sha512": "otZdBBC+fHVvO2oaL04HHyOFVI2jitLpP/zFwuBkpLNZfXtoo7pnbFFBDNYuZyBkkqNqvqibUrMkd+co4NQVVw==", "type": "package", "path": "microsoft.aspnetcore.jsonpatch/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.JsonPatch.dll", "lib/net462/Microsoft.AspNetCore.JsonPatch.xml", "lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll", "lib/net8.0/Microsoft.AspNetCore.JsonPatch.xml", "lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.dll", "lib/netstandard2.0/Microsoft.AspNetCore.JsonPatch.xml", "microsoft.aspnetcore.jsonpatch.8.0.2.nupkg.sha512", "microsoft.aspnetcore.jsonpatch.nuspec"]}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.2": {"sha512": "y9lCqaABdIy5hk7TkvslznP4nPiOKTwIXlHc6xjEuZg5BmXHqjlWjnH+ltBh2JckdjyEWas0csf757PYl8hYcw==", "type": "package", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll", "lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.xml", "microsoft.aspnetcore.mvc.newtonsoftjson.8.0.2.nupkg.sha512", "microsoft.aspnetcore.mvc.newtonsoftjson.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"sha512": "yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "ref/net461/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Data.SqlClient/5.1.5": {"sha512": "6kvhQjY5uBCdBccezFD2smfnpQjQ33cZtUZVrNvxlwoBu6uopM5INH6uSgLI7JRLtlQ3bMPwnhMq4kchsXeZ5w==", "type": "package", "path": "microsoft.data.sqlclient/5.1.5", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net462/Microsoft.Data.SqlClient.dll", "lib/net462/Microsoft.Data.SqlClient.pdb", "lib/net462/Microsoft.Data.SqlClient.xml", "lib/net462/de/Microsoft.Data.SqlClient.resources.dll", "lib/net462/es/Microsoft.Data.SqlClient.resources.dll", "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/it/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/Microsoft.Data.SqlClient.dll", "lib/net6.0/Microsoft.Data.SqlClient.pdb", "lib/net6.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.1/Microsoft.Data.SqlClient.xml", "microsoft.data.sqlclient.5.1.5.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net462/Microsoft.Data.SqlClient.dll", "ref/net462/Microsoft.Data.SqlClient.pdb", "ref/net462/Microsoft.Data.SqlClient.xml", "ref/net6.0/Microsoft.Data.SqlClient.dll", "ref/net6.0/Microsoft.Data.SqlClient.pdb", "ref/net6.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.0/Microsoft.Data.SqlClient.dll", "ref/netstandard2.0/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.1/Microsoft.Data.SqlClient.dll", "ref/netstandard2.1/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.1/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb"]}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"sha512": "wNGM5ZTQCa2blc9ikXQouybGiyMd6IHPVJvAlBEPtr6JepZEOYeDxGyprYvFVeOxlCXs7avridZQ0nYkHzQWCQ==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/5.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"sha512": "Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "type": "package", "path": "microsoft.extensions.apidescription.server/6.0.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.Extensions.ApiDescription.Server.props", "build/Microsoft.Extensions.ApiDescription.Server.targets", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets", "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "microsoft.extensions.apidescription.server.nuspec", "tools/Newtonsoft.Json.dll", "tools/dotnet-getdocument.deps.json", "tools/dotnet-getdocument.dll", "tools/dotnet-getdocument.runtimeconfig.json", "tools/net461-x86/GetDocument.Insider.exe", "tools/net461-x86/GetDocument.Insider.exe.config", "tools/net461-x86/Microsoft.Win32.Primitives.dll", "tools/net461-x86/System.AppContext.dll", "tools/net461-x86/System.Buffers.dll", "tools/net461-x86/System.Collections.Concurrent.dll", "tools/net461-x86/System.Collections.NonGeneric.dll", "tools/net461-x86/System.Collections.Specialized.dll", "tools/net461-x86/System.Collections.dll", "tools/net461-x86/System.ComponentModel.EventBasedAsync.dll", "tools/net461-x86/System.ComponentModel.Primitives.dll", "tools/net461-x86/System.ComponentModel.TypeConverter.dll", "tools/net461-x86/System.ComponentModel.dll", "tools/net461-x86/System.Console.dll", "tools/net461-x86/System.Data.Common.dll", "tools/net461-x86/System.Diagnostics.Contracts.dll", "tools/net461-x86/System.Diagnostics.Debug.dll", "tools/net461-x86/System.Diagnostics.DiagnosticSource.dll", "tools/net461-x86/System.Diagnostics.FileVersionInfo.dll", "tools/net461-x86/System.Diagnostics.Process.dll", "tools/net461-x86/System.Diagnostics.StackTrace.dll", "tools/net461-x86/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461-x86/System.Diagnostics.Tools.dll", "tools/net461-x86/System.Diagnostics.TraceSource.dll", "tools/net461-x86/System.Diagnostics.Tracing.dll", "tools/net461-x86/System.Drawing.Primitives.dll", "tools/net461-x86/System.Dynamic.Runtime.dll", "tools/net461-x86/System.Globalization.Calendars.dll", "tools/net461-x86/System.Globalization.Extensions.dll", "tools/net461-x86/System.Globalization.dll", "tools/net461-x86/System.IO.Compression.ZipFile.dll", "tools/net461-x86/System.IO.Compression.dll", "tools/net461-x86/System.IO.FileSystem.DriveInfo.dll", "tools/net461-x86/System.IO.FileSystem.Primitives.dll", "tools/net461-x86/System.IO.FileSystem.Watcher.dll", "tools/net461-x86/System.IO.FileSystem.dll", "tools/net461-x86/System.IO.IsolatedStorage.dll", "tools/net461-x86/System.IO.MemoryMappedFiles.dll", "tools/net461-x86/System.IO.Pipes.dll", "tools/net461-x86/System.IO.UnmanagedMemoryStream.dll", "tools/net461-x86/System.IO.dll", "tools/net461-x86/System.Linq.Expressions.dll", "tools/net461-x86/System.Linq.Parallel.dll", "tools/net461-x86/System.Linq.Queryable.dll", "tools/net461-x86/System.Linq.dll", "tools/net461-x86/System.Memory.dll", "tools/net461-x86/System.Net.Http.dll", "tools/net461-x86/System.Net.NameResolution.dll", "tools/net461-x86/System.Net.NetworkInformation.dll", "tools/net461-x86/System.Net.Ping.dll", "tools/net461-x86/System.Net.Primitives.dll", "tools/net461-x86/System.Net.Requests.dll", "tools/net461-x86/System.Net.Security.dll", "tools/net461-x86/System.Net.Sockets.dll", "tools/net461-x86/System.Net.WebHeaderCollection.dll", "tools/net461-x86/System.Net.WebSockets.Client.dll", "tools/net461-x86/System.Net.WebSockets.dll", "tools/net461-x86/System.Numerics.Vectors.dll", "tools/net461-x86/System.ObjectModel.dll", "tools/net461-x86/System.Reflection.Extensions.dll", "tools/net461-x86/System.Reflection.Primitives.dll", "tools/net461-x86/System.Reflection.dll", "tools/net461-x86/System.Resources.Reader.dll", "tools/net461-x86/System.Resources.ResourceManager.dll", "tools/net461-x86/System.Resources.Writer.dll", "tools/net461-x86/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461-x86/System.Runtime.CompilerServices.VisualC.dll", "tools/net461-x86/System.Runtime.Extensions.dll", "tools/net461-x86/System.Runtime.Handles.dll", "tools/net461-x86/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461-x86/System.Runtime.InteropServices.dll", "tools/net461-x86/System.Runtime.Numerics.dll", "tools/net461-x86/System.Runtime.Serialization.Formatters.dll", "tools/net461-x86/System.Runtime.Serialization.Json.dll", "tools/net461-x86/System.Runtime.Serialization.Primitives.dll", "tools/net461-x86/System.Runtime.Serialization.Xml.dll", "tools/net461-x86/System.Runtime.dll", "tools/net461-x86/System.Security.Claims.dll", "tools/net461-x86/System.Security.Cryptography.Algorithms.dll", "tools/net461-x86/System.Security.Cryptography.Csp.dll", "tools/net461-x86/System.Security.Cryptography.Encoding.dll", "tools/net461-x86/System.Security.Cryptography.Primitives.dll", "tools/net461-x86/System.Security.Cryptography.X509Certificates.dll", "tools/net461-x86/System.Security.Principal.dll", "tools/net461-x86/System.Security.SecureString.dll", "tools/net461-x86/System.Text.Encoding.Extensions.dll", "tools/net461-x86/System.Text.Encoding.dll", "tools/net461-x86/System.Text.RegularExpressions.dll", "tools/net461-x86/System.Threading.Overlapped.dll", "tools/net461-x86/System.Threading.Tasks.Parallel.dll", "tools/net461-x86/System.Threading.Tasks.dll", "tools/net461-x86/System.Threading.Thread.dll", "tools/net461-x86/System.Threading.ThreadPool.dll", "tools/net461-x86/System.Threading.Timer.dll", "tools/net461-x86/System.Threading.dll", "tools/net461-x86/System.ValueTuple.dll", "tools/net461-x86/System.Xml.ReaderWriter.dll", "tools/net461-x86/System.Xml.XDocument.dll", "tools/net461-x86/System.Xml.XPath.XDocument.dll", "tools/net461-x86/System.Xml.XPath.dll", "tools/net461-x86/System.Xml.XmlDocument.dll", "tools/net461-x86/System.Xml.XmlSerializer.dll", "tools/net461-x86/netstandard.dll", "tools/net461/GetDocument.Insider.exe", "tools/net461/GetDocument.Insider.exe.config", "tools/net461/Microsoft.Win32.Primitives.dll", "tools/net461/System.AppContext.dll", "tools/net461/System.Buffers.dll", "tools/net461/System.Collections.Concurrent.dll", "tools/net461/System.Collections.NonGeneric.dll", "tools/net461/System.Collections.Specialized.dll", "tools/net461/System.Collections.dll", "tools/net461/System.ComponentModel.EventBasedAsync.dll", "tools/net461/System.ComponentModel.Primitives.dll", "tools/net461/System.ComponentModel.TypeConverter.dll", "tools/net461/System.ComponentModel.dll", "tools/net461/System.Console.dll", "tools/net461/System.Data.Common.dll", "tools/net461/System.Diagnostics.Contracts.dll", "tools/net461/System.Diagnostics.Debug.dll", "tools/net461/System.Diagnostics.DiagnosticSource.dll", "tools/net461/System.Diagnostics.FileVersionInfo.dll", "tools/net461/System.Diagnostics.Process.dll", "tools/net461/System.Diagnostics.StackTrace.dll", "tools/net461/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461/System.Diagnostics.Tools.dll", "tools/net461/System.Diagnostics.TraceSource.dll", "tools/net461/System.Diagnostics.Tracing.dll", "tools/net461/System.Drawing.Primitives.dll", "tools/net461/System.Dynamic.Runtime.dll", "tools/net461/System.Globalization.Calendars.dll", "tools/net461/System.Globalization.Extensions.dll", "tools/net461/System.Globalization.dll", "tools/net461/System.IO.Compression.ZipFile.dll", "tools/net461/System.IO.Compression.dll", "tools/net461/System.IO.FileSystem.DriveInfo.dll", "tools/net461/System.IO.FileSystem.Primitives.dll", "tools/net461/System.IO.FileSystem.Watcher.dll", "tools/net461/System.IO.FileSystem.dll", "tools/net461/System.IO.IsolatedStorage.dll", "tools/net461/System.IO.MemoryMappedFiles.dll", "tools/net461/System.IO.Pipes.dll", "tools/net461/System.IO.UnmanagedMemoryStream.dll", "tools/net461/System.IO.dll", "tools/net461/System.Linq.Expressions.dll", "tools/net461/System.Linq.Parallel.dll", "tools/net461/System.Linq.Queryable.dll", "tools/net461/System.Linq.dll", "tools/net461/System.Memory.dll", "tools/net461/System.Net.Http.dll", "tools/net461/System.Net.NameResolution.dll", "tools/net461/System.Net.NetworkInformation.dll", "tools/net461/System.Net.Ping.dll", "tools/net461/System.Net.Primitives.dll", "tools/net461/System.Net.Requests.dll", "tools/net461/System.Net.Security.dll", "tools/net461/System.Net.Sockets.dll", "tools/net461/System.Net.WebHeaderCollection.dll", "tools/net461/System.Net.WebSockets.Client.dll", "tools/net461/System.Net.WebSockets.dll", "tools/net461/System.Numerics.Vectors.dll", "tools/net461/System.ObjectModel.dll", "tools/net461/System.Reflection.Extensions.dll", "tools/net461/System.Reflection.Primitives.dll", "tools/net461/System.Reflection.dll", "tools/net461/System.Resources.Reader.dll", "tools/net461/System.Resources.ResourceManager.dll", "tools/net461/System.Resources.Writer.dll", "tools/net461/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461/System.Runtime.CompilerServices.VisualC.dll", "tools/net461/System.Runtime.Extensions.dll", "tools/net461/System.Runtime.Handles.dll", "tools/net461/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461/System.Runtime.InteropServices.dll", "tools/net461/System.Runtime.Numerics.dll", "tools/net461/System.Runtime.Serialization.Formatters.dll", "tools/net461/System.Runtime.Serialization.Json.dll", "tools/net461/System.Runtime.Serialization.Primitives.dll", "tools/net461/System.Runtime.Serialization.Xml.dll", "tools/net461/System.Runtime.dll", "tools/net461/System.Security.Claims.dll", "tools/net461/System.Security.Cryptography.Algorithms.dll", "tools/net461/System.Security.Cryptography.Csp.dll", "tools/net461/System.Security.Cryptography.Encoding.dll", "tools/net461/System.Security.Cryptography.Primitives.dll", "tools/net461/System.Security.Cryptography.X509Certificates.dll", "tools/net461/System.Security.Principal.dll", "tools/net461/System.Security.SecureString.dll", "tools/net461/System.Text.Encoding.Extensions.dll", "tools/net461/System.Text.Encoding.dll", "tools/net461/System.Text.RegularExpressions.dll", "tools/net461/System.Threading.Overlapped.dll", "tools/net461/System.Threading.Tasks.Parallel.dll", "tools/net461/System.Threading.Tasks.dll", "tools/net461/System.Threading.Thread.dll", "tools/net461/System.Threading.ThreadPool.dll", "tools/net461/System.Threading.Timer.dll", "tools/net461/System.Threading.dll", "tools/net461/System.ValueTuple.dll", "tools/net461/System.Xml.ReaderWriter.dll", "tools/net461/System.Xml.XDocument.dll", "tools/net461/System.Xml.XPath.XDocument.dll", "tools/net461/System.Xml.XPath.dll", "tools/net461/System.Xml.XmlDocument.dll", "tools/net461/System.Xml.XmlSerializer.dll", "tools/net461/netstandard.dll", "tools/netcoreapp2.1/GetDocument.Insider.deps.json", "tools/netcoreapp2.1/GetDocument.Insider.dll", "tools/netcoreapp2.1/GetDocument.Insider.runtimeconfig.json", "tools/netcoreapp2.1/System.Diagnostics.DiagnosticSource.dll"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"sha512": "lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"sha512": "RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"sha512": "MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"sha512": "+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/9.0.0": {"sha512": "saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "type": "package", "path": "microsoft.extensions.dependencymodel/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/net9.0/Microsoft.Extensions.DependencyModel.dll", "lib/net9.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"sha512": "1K8P7XzuzX8W8pmXcZjcrqS6x5eSSdvhQohmcpgiQNY/HlDAlnrhR9dvlURfFz428A+RTCJpUyB+aKTA6AgVcQ==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"sha512": "uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"sha512": "yUKJgu81ExjvqbNWqZKshBbLntZMbMVz/P7Way2SBx7bMqA08Mfdc9O7hWDKAiSp+zPUGT6LKcSCQIPeDK+CCw==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.0": {"sha512": "crjWyORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "type": "package", "path": "microsoft.extensions.logging/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"sha512": "g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.0": {"sha512": "y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "type": "package", "path": "microsoft.extensions.options/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.0": {"sha512": "N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "type": "package", "path": "microsoft.extensions.primitives/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Identity.Client/4.56.0": {"sha512": "rr4zbidvHy9r4NvOAs5hdd964Ao2A0pAeFBJKR95u1CJAVzbd1p6tPTXUZ+5ld0cfThiVSGvz6UHwY6JjraTpA==", "type": "package", "path": "microsoft.identity.client/4.56.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid10.0/Microsoft.Identity.Client.dll", "lib/monoandroid10.0/Microsoft.Identity.Client.xml", "lib/monoandroid90/Microsoft.Identity.Client.dll", "lib/monoandroid90/Microsoft.Identity.Client.xml", "lib/net45/Microsoft.Identity.Client.dll", "lib/net45/Microsoft.Identity.Client.xml", "lib/net461/Microsoft.Identity.Client.dll", "lib/net461/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/net6.0-windows7.0/Microsoft.Identity.Client.dll", "lib/net6.0-windows7.0/Microsoft.Identity.Client.xml", "lib/net6.0/Microsoft.Identity.Client.dll", "lib/net6.0/Microsoft.Identity.Client.xml", "lib/netcoreapp2.1/Microsoft.Identity.Client.dll", "lib/netcoreapp2.1/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "lib/uap10.0.17763/Microsoft.Identity.Client.dll", "lib/uap10.0.17763/Microsoft.Identity.Client.pri", "lib/uap10.0.17763/Microsoft.Identity.Client.xml", "lib/xamarinios10/Microsoft.Identity.Client.dll", "lib/xamarinios10/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.56.0.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"sha512": "H12YAzEGK55vZ+QpxUzozhW8ZZtgPDuWvgA0JbdIR9UhMUplj29JhIgE2imuH8W2Nw9D8JKygR1uxRFtpSNcrg==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.56.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"sha512": "33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ==", "type": "package", "path": "microsoft.identitymodel.abstractions/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"sha512": "cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/7.1.2": {"sha512": "YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "type": "package", "path": "microsoft.identitymodel.logging/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.7.1.2.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/7.1.2": {"sha512": "SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "type": "package", "path": "microsoft.identitymodel.protocols/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"sha512": "6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/7.1.2": {"sha512": "oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "type": "package", "path": "microsoft.identitymodel.tokens/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.7.1.2.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.NETCore.Platforms/1.0.1": {"sha512": "2G6OjjJzwBfNOO8myRV/nFrbTw5iA+DEm0N+qUqhrOmaVtn4pC77h38I1jsXGw5VH55+dPfQsqHD0We9sCl9FQ==", "type": "package", "path": "microsoft.netcore.platforms/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.0.1.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.0.1": {"sha512": "rkn+fKobF/cbWfnnfBOQHKVKIOpxMZBvlSHkqDWgBpwGDcLRduvs3D9OLGeV6GWGvVwNlVi2CBbTjuPmtHvyNw==", "type": "package", "path": "microsoft.netcore.targets/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.0.1.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.OpenApi/1.2.3": {"sha512": "Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "type": "package", "path": "microsoft.openapi/1.2.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net46/Microsoft.OpenApi.dll", "lib/net46/Microsoft.OpenApi.pdb", "lib/net46/Microsoft.OpenApi.xml", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.2.3.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Microsoft.SqlServer.Server/1.0.0": {"sha512": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "type": "package", "path": "microsoft.sqlserver.server/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net46/Microsoft.SqlServer.Server.dll", "lib/net46/Microsoft.SqlServer.Server.pdb", "lib/net46/Microsoft.SqlServer.Server.xml", "lib/netstandard2.0/Microsoft.SqlServer.Server.dll", "lib/netstandard2.0/Microsoft.SqlServer.Server.pdb", "lib/netstandard2.0/Microsoft.SqlServer.Server.xml", "microsoft.sqlserver.server.1.0.0.nupkg.sha512", "microsoft.sqlserver.server.nuspec"]}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.19.5": {"sha512": "Kaa1rBZdJFq5A0qgAcl6Bmk/UqLXTq9acEqxUlPEBA8oscmakLfkvuSXfG7Wa9t1/keaT85EuuDNgOo+Z9VYOQ==", "type": "package", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.19.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "EULA.md", "ThirdPartyNotices.txt", "build/Container.props", "build/Container.targets", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.props", "build/Microsoft.VisualStudio.Azure.Containers.Tools.Targets.targets", "build/Rules/GeneralBrowseObject.xaml", "build/Rules/cs-CZ/GeneralBrowseObject.xaml", "build/Rules/de-DE/GeneralBrowseObject.xaml", "build/Rules/es-ES/GeneralBrowseObject.xaml", "build/Rules/fr-FR/GeneralBrowseObject.xaml", "build/Rules/it-IT/GeneralBrowseObject.xaml", "build/Rules/ja-JP/GeneralBrowseObject.xaml", "build/Rules/ko-KR/GeneralBrowseObject.xaml", "build/Rules/pl-PL/GeneralBrowseObject.xaml", "build/Rules/pt-BR/GeneralBrowseObject.xaml", "build/Rules/ru-RU/GeneralBrowseObject.xaml", "build/Rules/tr-TR/GeneralBrowseObject.xaml", "build/Rules/zh-CN/GeneralBrowseObject.xaml", "build/Rules/zh-TW/GeneralBrowseObject.xaml", "build/ToolsTarget.props", "build/ToolsTarget.targets", "icon.png", "microsoft.visualstudio.azure.containers.tools.targets.1.19.5.nupkg.sha512", "microsoft.visualstudio.azure.containers.tools.targets.nuspec", "tools/Microsoft.VisualStudio.Containers.Tools.Common.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Shared.dll", "tools/Microsoft.VisualStudio.Containers.Tools.Tasks.dll", "tools/Newtonsoft.Json.dll", "tools/System.Security.Principal.Windows.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/cs/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/de/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/es/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/fr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/it/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ja/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ko/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pl/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/pt-BR/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/ru/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/tr/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/utils/KillProcess.exe", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hans/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Common.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Shared.resources.dll", "tools/zh-Hant/Microsoft.VisualStudio.Containers.Tools.Tasks.resources.dll"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/6.0.0": {"sha512": "hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "type": "package", "path": "microsoft.win32.systemevents/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.6.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "MongoDB.Bson/2.24.0": {"sha512": "n8CWaA4iTuoEQYv0+FSKNTX/hJozFQa5EgSILVNPhTGHcrbABHhpVrT1NwRRAAS6sUb8ZyhHmLPBa88LJemptA==", "type": "package", "path": "mongodb.bson/2.24.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net472/MongoDB.Bson.dll", "lib/net472/MongoDB.Bson.xml", "lib/netstandard2.0/MongoDB.Bson.dll", "lib/netstandard2.0/MongoDB.Bson.xml", "lib/netstandard2.1/MongoDB.Bson.dll", "lib/netstandard2.1/MongoDB.Bson.xml", "mongodb.bson.2.24.0.nupkg.sha512", "mongodb.bson.nuspec", "packageIcon.png"]}, "MongoDB.Driver/2.24.0": {"sha512": "j1q11iMk3LN38ze6jgV1ATp+WKVVQbsGrhFkuOcHwRNtIk70TpLKjOD1Z3CCkyrzxCsUyhwk745tK2ASNOI4WA==", "type": "package", "path": "mongodb.driver/2.24.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net472/MongoDB.Driver.dll", "lib/net472/MongoDB.Driver.xml", "lib/netstandard2.0/MongoDB.Driver.dll", "lib/netstandard2.0/MongoDB.Driver.xml", "lib/netstandard2.1/MongoDB.Driver.dll", "lib/netstandard2.1/MongoDB.Driver.xml", "mongodb.driver.2.24.0.nupkg.sha512", "mongodb.driver.nuspec", "packageIcon.png"]}, "MongoDB.Driver.Core/2.24.0": {"sha512": "UW0yadpMPi9+MtLHy6onpol3D9tXMRg61P0ROnij+h35EOr0vt/nxvlPrDcUjl3SvttvpsEXQKxb2lQShBA1dA==", "type": "package", "path": "mongodb.driver.core/2.24.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "THIRD-PARTY-NOTICES", "lib/net472/MongoDB.Driver.Core.dll", "lib/net472/MongoDB.Driver.Core.xml", "lib/netstandard2.0/MongoDB.Driver.Core.dll", "lib/netstandard2.0/MongoDB.Driver.Core.xml", "lib/netstandard2.1/MongoDB.Driver.Core.dll", "lib/netstandard2.1/MongoDB.Driver.Core.xml", "mongodb.driver.core.2.24.0.nupkg.sha512", "mongodb.driver.core.nuspec", "packageIcon.png"]}, "MongoDB.Libmongocrypt/1.8.2": {"sha512": "z/8JCULSHM1+mzkau0ivIkU9kIn8JEFFSkmYTSaMaWMMHt96JjUtMKuXxeGNGSnHZ5290ZPKIlQfjoWFk2sKog==", "type": "package", "path": "mongodb.libmongocrypt/1.8.2", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "build/MongoDB.Libmongocrypt.targets", "content/libmongocrypt.dylib", "content/libmongocrypt.so", "content/mongocrypt.dll", "contentFiles/any/netstandard2.0/libmongocrypt.dylib", "contentFiles/any/netstandard2.0/libmongocrypt.so", "contentFiles/any/netstandard2.0/mongocrypt.dll", "contentFiles/any/netstandard2.1/libmongocrypt.dylib", "contentFiles/any/netstandard2.1/libmongocrypt.so", "contentFiles/any/netstandard2.1/mongocrypt.dll", "lib/netstandard2.0/MongoDB.Libmongocrypt.dll", "lib/netstandard2.1/MongoDB.Libmongocrypt.dll", "mongodb.libmongocrypt.1.8.2.nupkg.sha512", "mongodb.libmongocrypt.nuspec", "runtimes/linux/native/libmongocrypt.so", "runtimes/osx/native/libmongocrypt.dylib", "runtimes/win/native/mongocrypt.dll"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Newtonsoft.Json.Bson/1.0.2": {"sha512": "QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "type": "package", "path": "newtonsoft.json.bson/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net45/Newtonsoft.Json.Bson.dll", "lib/net45/Newtonsoft.Json.Bson.pdb", "lib/net45/Newtonsoft.Json.Bson.xml", "lib/netstandard1.3/Newtonsoft.Json.Bson.dll", "lib/netstandard1.3/Newtonsoft.Json.Bson.pdb", "lib/netstandard1.3/Newtonsoft.Json.Bson.xml", "lib/netstandard2.0/Newtonsoft.Json.Bson.dll", "lib/netstandard2.0/Newtonsoft.Json.Bson.pdb", "lib/netstandard2.0/Newtonsoft.Json.Bson.xml", "newtonsoft.json.bson.1.0.2.nupkg.sha512", "newtonsoft.json.bson.nuspec"]}, "Serilog/4.2.0": {"sha512": "gmoWVOvKgbME8TYR+gwMf7osROiWAURterc6Rt2dQyX7wtjZYpqFiA/pY6ztjGQKKV62GGCyOcmtP1UKMHgSmA==", "type": "package", "path": "serilog/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net8.0/Serilog.dll", "lib/net8.0/Serilog.xml", "lib/net9.0/Serilog.dll", "lib/net9.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "serilog.4.2.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.AspNetCore/9.0.0": {"sha512": "JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "type": "package", "path": "serilog.aspnetcore/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.AspNetCore.dll", "lib/net462/Serilog.AspNetCore.xml", "lib/net8.0/Serilog.AspNetCore.dll", "lib/net8.0/Serilog.AspNetCore.xml", "lib/net9.0/Serilog.AspNetCore.dll", "lib/net9.0/Serilog.AspNetCore.xml", "lib/netstandard2.0/Serilog.AspNetCore.dll", "lib/netstandard2.0/Serilog.AspNetCore.xml", "lib/netstandard2.1/Serilog.AspNetCore.dll", "lib/netstandard2.1/Serilog.AspNetCore.xml", "serilog.aspnetcore.9.0.0.nupkg.sha512", "serilog.aspnetcore.nuspec"]}, "Serilog.Enrichers.Environment/2.3.0": {"sha512": "AdZXURQ0dQCCjst3Jn3lwFtGicWjGE4wov9E5BPc4N5cruGmd2y9wprCYEjFteU84QMbxk35fpeTuHs6M4VGYw==", "type": "package", "path": "serilog.enrichers.environment/2.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Serilog.Enrichers.Environment.dll", "lib/netstandard1.3/Serilog.Enrichers.Environment.dll", "lib/netstandard1.5/Serilog.Enrichers.Environment.dll", "lib/netstandard2.0/Serilog.Enrichers.Environment.dll", "serilog.enrichers.environment.2.3.0.nupkg.sha512", "serilog.enrichers.environment.nuspec"]}, "Serilog.Extensions.Hosting/9.0.0": {"sha512": "u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "type": "package", "path": "serilog.extensions.hosting/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Extensions.Hosting.dll", "lib/net462/Serilog.Extensions.Hosting.xml", "lib/net8.0/Serilog.Extensions.Hosting.dll", "lib/net8.0/Serilog.Extensions.Hosting.xml", "lib/net9.0/Serilog.Extensions.Hosting.dll", "lib/net9.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.0/Serilog.Extensions.Hosting.dll", "lib/netstandard2.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.1/Serilog.Extensions.Hosting.dll", "lib/netstandard2.1/Serilog.Extensions.Hosting.xml", "serilog.extensions.hosting.9.0.0.nupkg.sha512", "serilog.extensions.hosting.nuspec"]}, "Serilog.Extensions.Logging/9.0.0": {"sha512": "NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "type": "package", "path": "serilog.extensions.logging/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Extensions.Logging.dll", "lib/net462/Serilog.Extensions.Logging.xml", "lib/net8.0/Serilog.Extensions.Logging.dll", "lib/net8.0/Serilog.Extensions.Logging.xml", "lib/net9.0/Serilog.Extensions.Logging.dll", "lib/net9.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.0/Serilog.Extensions.Logging.dll", "lib/netstandard2.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.1/Serilog.Extensions.Logging.dll", "lib/netstandard2.1/Serilog.Extensions.Logging.xml", "serilog-extension-nuget.png", "serilog.extensions.logging.9.0.0.nupkg.sha512", "serilog.extensions.logging.nuspec"]}, "Serilog.Formatting.Compact/3.0.0": {"sha512": "wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "type": "package", "path": "serilog.formatting.compact/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Formatting.Compact.dll", "lib/net462/Serilog.Formatting.Compact.xml", "lib/net471/Serilog.Formatting.Compact.dll", "lib/net471/Serilog.Formatting.Compact.xml", "lib/net6.0/Serilog.Formatting.Compact.dll", "lib/net6.0/Serilog.Formatting.Compact.xml", "lib/net8.0/Serilog.Formatting.Compact.dll", "lib/net8.0/Serilog.Formatting.Compact.xml", "lib/netstandard2.0/Serilog.Formatting.Compact.dll", "lib/netstandard2.0/Serilog.Formatting.Compact.xml", "serilog-extension-nuget.png", "serilog.formatting.compact.3.0.0.nupkg.sha512", "serilog.formatting.compact.nuspec"]}, "Serilog.Settings.Configuration/9.0.0": {"sha512": "4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "type": "package", "path": "serilog.settings.configuration/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Settings.Configuration.dll", "lib/net462/Serilog.Settings.Configuration.xml", "lib/net8.0/Serilog.Settings.Configuration.dll", "lib/net8.0/Serilog.Settings.Configuration.xml", "lib/net9.0/Serilog.Settings.Configuration.dll", "lib/net9.0/Serilog.Settings.Configuration.xml", "lib/netstandard2.0/Serilog.Settings.Configuration.dll", "lib/netstandard2.0/Serilog.Settings.Configuration.xml", "serilog.settings.configuration.9.0.0.nupkg.sha512", "serilog.settings.configuration.nuspec"]}, "Serilog.Sinks.Async/1.5.0": {"sha512": "csHYIqAwI4Gy9oAhXYRwxGrQEAtBg3Ep7WaCzsnA1cZuBZjVAU0n7hWaJhItjO7hbLHh/9gRVxALCUB4Dv+gZw==", "type": "package", "path": "serilog.sinks.async/1.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Serilog.Sinks.Async.dll", "lib/net45/Serilog.Sinks.Async.xml", "lib/net461/Serilog.Sinks.Async.dll", "lib/net461/Serilog.Sinks.Async.xml", "lib/netstandard1.1/Serilog.Sinks.Async.dll", "lib/netstandard1.1/Serilog.Sinks.Async.xml", "lib/netstandard2.0/Serilog.Sinks.Async.dll", "lib/netstandard2.0/Serilog.Sinks.Async.xml", "serilog-sink-nuget.png", "serilog.sinks.async.1.5.0.nupkg.sha512", "serilog.sinks.async.nuspec"]}, "Serilog.Sinks.Console/6.0.0": {"sha512": "fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "type": "package", "path": "serilog.sinks.console/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Console.dll", "lib/net462/Serilog.Sinks.Console.xml", "lib/net471/Serilog.Sinks.Console.dll", "lib/net471/Serilog.Sinks.Console.xml", "lib/net6.0/Serilog.Sinks.Console.dll", "lib/net6.0/Serilog.Sinks.Console.xml", "lib/net8.0/Serilog.Sinks.Console.dll", "lib/net8.0/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.6.0.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.Debug/3.0.0": {"sha512": "4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "type": "package", "path": "serilog.sinks.debug/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Debug.dll", "lib/net462/Serilog.Sinks.Debug.xml", "lib/net471/Serilog.Sinks.Debug.dll", "lib/net471/Serilog.Sinks.Debug.xml", "lib/net6.0/Serilog.Sinks.Debug.dll", "lib/net6.0/Serilog.Sinks.Debug.xml", "lib/net8.0/Serilog.Sinks.Debug.dll", "lib/net8.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.0/Serilog.Sinks.Debug.dll", "lib/netstandard2.0/Serilog.Sinks.Debug.xml", "serilog.sinks.debug.3.0.0.nupkg.sha512", "serilog.sinks.debug.nuspec"]}, "Serilog.Sinks.File/6.0.0": {"sha512": "lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "type": "package", "path": "serilog.sinks.file/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Sinks.File.dll", "lib/net462/Serilog.Sinks.File.xml", "lib/net471/Serilog.Sinks.File.dll", "lib/net471/Serilog.Sinks.File.xml", "lib/net6.0/Serilog.Sinks.File.dll", "lib/net6.0/Serilog.Sinks.File.xml", "lib/net8.0/Serilog.Sinks.File.dll", "lib/net8.0/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.xml", "serilog-sink-nuget.png", "serilog.sinks.file.6.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"sha512": "NDWR7m3PalVlGEq3rzoktrXikjFMLmpwF0HI4sowo8YDdU+gqPlTHlDQiOGxHfB0sTfjPA9JjA7ctKG9zqjGkw==", "type": "package", "path": "serilog.sinks.periodicbatching/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.PeriodicBatching.dll", "lib/net45/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard1.1/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard1.1/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard1.2/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard1.2/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard2.0/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard2.0/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.xml", "serilog.sinks.periodicbatching.3.1.0.nupkg.sha512", "serilog.sinks.periodicbatching.nuspec"]}, "Serilog.Sinks.RollingFile/3.3.0": {"sha512": "2lT5X1r3GH4P0bRWJfhA7etGl8Q2Ipw9AACvtAHWRUSpYZ42NGVyHoVs2ALBZ/cAkkS+tA4jl80Zie144eLQPg==", "type": "package", "path": "serilog.sinks.rollingfile/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Serilog.Sinks.RollingFile.dll", "lib/net45/Serilog.Sinks.RollingFile.xml", "lib/netstandard1.3/Serilog.Sinks.RollingFile.dll", "lib/netstandard1.3/Serilog.Sinks.RollingFile.xml", "serilog.sinks.rollingfile.3.3.0.nupkg.sha512", "serilog.sinks.rollingfile.nuspec"]}, "Serilog.Sinks.Seq/6.0.0": {"sha512": "LtxlH5xE3ZPxmCYL5+I8tPzytnR91xfFFIIUIcpoGq69a45eyFkrVMonApww+B08a0I++GfM7jP1oB6GBhOR1w==", "type": "package", "path": "serilog.sinks.seq/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net6.0/Serilog.Sinks.Seq.dll", "lib/net6.0/Serilog.Sinks.Seq.xml", "lib/netstandard2.0/Serilog.Sinks.Seq.dll", "lib/netstandard2.0/Serilog.Sinks.Seq.xml", "serilog.sinks.seq.6.0.0.nupkg.sha512", "serilog.sinks.seq.nuspec"]}, "SharpCompress/0.30.1": {"sha512": "XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "type": "package", "path": "sharpcompress/0.30.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/SharpCompress.dll", "lib/net5.0/SharpCompress.dll", "lib/netcoreapp3.1/SharpCompress.dll", "lib/netstandard2.0/SharpCompress.dll", "lib/netstandard2.1/SharpCompress.dll", "sharpcompress.0.30.1.nupkg.sha512", "sharpcompress.nuspec"]}, "SmartIT.DebugTraceHelper/1.0.5": {"sha512": "+jjOTq0ouhwHJmcEOebQAkWtf1cAFzhJGp5t9Pvly0kj2DnlJyDg520GMfABhbFcxouFLErtgGpxqG5/rHxwhQ==", "type": "package", "path": "smartit.debugtracehelper/1.0.5", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp2.0/SmartIT.DebugHelper.dll", "smartit.debugtracehelper.1.0.5.nupkg.sha512", "smartit.debugtracehelper.nuspec"]}, "Snappier/1.0.0": {"sha512": "rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "type": "package", "path": "snappier/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "COPYING.txt", "lib/net5.0/Snappier.dll", "lib/net5.0/Snappier.xml", "lib/netcoreapp3.0/Snappier.dll", "lib/netcoreapp3.0/Snappier.xml", "lib/netstandard2.0/Snappier.dll", "lib/netstandard2.0/Snappier.xml", "lib/netstandard2.1/Snappier.dll", "lib/netstandard2.1/Snappier.xml", "snappier.1.0.0.nupkg.sha512", "snappier.nuspec"]}, "Swashbuckle.AspNetCore/6.5.0": {"sha512": "FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "type": "package", "path": "swashbuckle.aspnetcore/6.5.0", "files": [".nupkg.metadata", ".signature.p7s", "build/Swashbuckle.AspNetCore.props", "swashbuckle.aspnetcore.6.5.0.nupkg.sha512", "swashbuckle.aspnetcore.nuspec"]}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"sha512": "XWmCmqyFmoItXKFsQSwQbEAsjDKcxlNf1l+/Ki42hcb6LjKL8m5Db69OTvz5vLonMSRntYO1XLqz0OP+n3vKnA==", "type": "package", "path": "swashbuckle.aspnetcore.swagger/6.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net7.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net7.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.xml", "swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512", "swashbuckle.aspnetcore.swagger.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"sha512": "Y/qW8Qdg9OEs7V013tt+94OdPxbRdbhcEbw4NiwGvf4YBcfhL/y7qp/Mjv/cENsQ2L3NqJ2AOu94weBy/h4KvA==", "type": "package", "path": "swashbuckle.aspnetcore.swaggergen/6.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512", "swashbuckle.aspnetcore.swaggergen.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"sha512": "OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "type": "package", "path": "swashbuckle.aspnetcore.swaggerui/6.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512", "swashbuckle.aspnetcore.swaggerui.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Configuration.ConfigurationManager/6.0.1": {"sha512": "jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "type": "package", "path": "system.configuration.configurationmanager/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/net461/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.dll", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.6.0.1.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/6.0.1": {"sha512": "KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.DiagnosticSource.dll", "lib/net461/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/6.0.0": {"sha512": "NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "type": "package", "path": "system.drawing.common/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/net461/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/netcoreapp3.1/System.Drawing.Common.dll", "lib/netcoreapp3.1/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/unix/lib/net6.0/System.Drawing.Common.dll", "runtimes/unix/lib/net6.0/System.Drawing.Common.xml", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.xml", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.xml", "system.drawing.common.6.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Formats.Asn1/5.0.0": {"sha512": "MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "type": "package", "path": "system.formats.asn1/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Formats.Asn1.dll", "lib/net461/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.5.0.0.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"sha512": "Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "type": "package", "path": "system.identitymodel.tokens.jwt/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO/4.1.0": {"sha512": "3KlTJceQc3gnGIaHZ7UBZO26SHL1SHE4ddrmiwumFnId+CEHP+O8r386tZKaE6zlk5/mF8vifMBzHj9SaXN+mQ==", "type": "package", "path": "system.io/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.1.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.FileSystem.AccessControl/5.0.0": {"sha512": "SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "type": "package", "path": "system.io.filesystem.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.IO.FileSystem.AccessControl.dll", "lib/net461/System.IO.FileSystem.AccessControl.dll", "lib/net461/System.IO.FileSystem.AccessControl.xml", "lib/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "lib/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "ref/net46/System.IO.FileSystem.AccessControl.dll", "ref/net461/System.IO.FileSystem.AccessControl.dll", "ref/net461/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "ref/netstandard1.3/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/de/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/es/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/it/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.AccessControl.xml", "ref/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "ref/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "runtimes/win/lib/net46/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/net461/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/net461/System.IO.FileSystem.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512", "system.io.filesystem.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO.FileSystem.Primitives/4.0.1": {"sha512": "kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ==", "type": "package", "path": "system.io.filesystem.primitives/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.primitives.4.0.1.nupkg.sha512", "system.io.filesystem.primitives.nuspec"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory.Data/1.0.2": {"sha512": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "type": "package", "path": "system.memory.data/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.1.0.2.nupkg.sha512", "system.memory.data.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection/4.1.0": {"sha512": "JCKANJ0TI7kzoQzuwB/OoJANy1Lg338B6+JVacPl4TpUwi3cReg3nMLplMq2uqYfHFQpKIlHAUVAJlImZz/4ng==", "type": "package", "path": "system.reflection/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.1.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Primitives/4.0.1": {"sha512": "4inTox4wTBaDhB7V3mPvp9XlCbeGYWVEM9/fXALd52vNEAVisc1BoVWQPuUuD0Ga//dNbA/WeMy9u9mzLxGTHQ==", "type": "package", "path": "system.reflection.primitives/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.0.1.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Runtime/4.1.0": {"sha512": "v6c/4Yaa9uWsq+JMhnOFewrYkgdNHNG2eMKuNqRn8P733rNXeRCGvV5FkkjBXn2dbVkPXOsO0xjsEeM1q2zC0g==", "type": "package", "path": "system.runtime/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.1.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Caching/6.0.0": {"sha512": "E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "type": "package", "path": "system.runtime.caching/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.Caching.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/_._", "lib/net6.0/System.Runtime.Caching.dll", "lib/net6.0/System.Runtime.Caching.xml", "lib/netcoreapp3.1/System.Runtime.Caching.dll", "lib/netcoreapp3.1/System.Runtime.Caching.xml", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/_._", "runtimes/win/lib/net6.0/System.Runtime.Caching.dll", "runtimes/win/lib/net6.0/System.Runtime.Caching.xml", "runtimes/win/lib/netcoreapp3.1/System.Runtime.Caching.dll", "runtimes/win/lib/netcoreapp3.1/System.Runtime.Caching.xml", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.xml", "system.runtime.caching.6.0.0.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.Handles/4.0.1": {"sha512": "nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "type": "package", "path": "system.runtime.handles/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.0.1.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.1.0": {"sha512": "16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "type": "package", "path": "system.runtime.interopservices/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.1.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Cng/5.0.0": {"sha512": "jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "type": "package", "path": "system.security.cryptography.cng/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.xml", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.xml", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.xml", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.xml", "lib/netstandard2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard2.1/System.Security.Cryptography.Cng.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/netstandard2.1/System.Security.Cryptography.Cng.dll", "ref/netstandard2.1/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.5.0.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.ProtectedData/6.0.0": {"sha512": "rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "type": "package", "path": "system.security.cryptography.protecteddata/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/6.0.0": {"sha512": "T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "type": "package", "path": "system.security.permissions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Permissions.dll", "lib/net461/System.Security.Permissions.xml", "lib/net5.0/System.Security.Permissions.dll", "lib/net5.0/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/netcoreapp3.1/System.Security.Permissions.dll", "lib/netcoreapp3.1/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "runtimes/win/lib/net461/System.Security.Permissions.dll", "runtimes/win/lib/net461/System.Security.Permissions.xml", "system.security.permissions.6.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.0.11": {"sha512": "U3gGeMlDZXxCEiY4DwVLSacg+DFWCvoiX+JThA/rvw37Sqrku7sEFeVBBBMBnfB6FeZHsyDx85HlKL19x0HtZA==", "type": "package", "path": "system.text.encoding/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.0.11.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/6.0.0": {"sha512": "ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "type": "package", "path": "system.text.encoding.codepages/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.6.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding.Extensions/4.0.11": {"sha512": "jtbiTDtvfLYgXn8PTfWI+SiBs51rrmO4AAckx4KR6vFK9Wzf6tI8kcRdsYQNwriUeQ1+CtQbM1W4cMbLXnj/OQ==", "type": "package", "path": "system.text.encoding.extensions/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.Extensions.dll", "ref/netcore50/System.Text.Encoding.Extensions.xml", "ref/netcore50/de/System.Text.Encoding.Extensions.xml", "ref/netcore50/es/System.Text.Encoding.Extensions.xml", "ref/netcore50/fr/System.Text.Encoding.Extensions.xml", "ref/netcore50/it/System.Text.Encoding.Extensions.xml", "ref/netcore50/ja/System.Text.Encoding.Extensions.xml", "ref/netcore50/ko/System.Text.Encoding.Extensions.xml", "ref/netcore50/ru/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/System.Text.Encoding.Extensions.dll", "ref/netstandard1.0/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/System.Text.Encoding.Extensions.dll", "ref/netstandard1.3/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.extensions.4.0.11.nupkg.sha512", "system.text.encoding.extensions.nuspec"]}, "System.Text.Encodings.Web/6.0.0": {"sha512": "Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "type": "package", "path": "system.text.encodings.web/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/netcoreapp3.1/System.Text.Encodings.Web.dll", "lib/netcoreapp3.1/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.6.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/4.7.2": {"sha512": "TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "type": "package", "path": "system.text.json/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/netcoreapp3.0/System.Text.Json.dll", "lib/netcoreapp3.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.4.7.2.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks/4.0.11": {"sha512": "k1S4Gc6IGwtHGT8188RSeGaX86Qw/wnrgNLshJvsdNUOPP9etMmo8S07c+UlOAx4K/xLuN9ivA1bD0LVurtIxQ==", "type": "package", "path": "system.threading.tasks/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.0.11.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Windows.Extensions/6.0.0": {"sha512": "IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "type": "package", "path": "system.windows.extensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/netcoreapp3.1/System.Windows.Extensions.dll", "lib/netcoreapp3.1/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.dll", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.xml", "system.windows.extensions.6.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}, "ZstdSharp.Port/0.7.3": {"sha512": "U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "type": "package", "path": "zstdsharp.port/0.7.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/ZstdSharp.dll", "lib/net5.0/ZstdSharp.dll", "lib/net6.0/ZstdSharp.dll", "lib/net7.0/ZstdSharp.dll", "lib/netcoreapp3.1/ZstdSharp.dll", "lib/netstandard2.0/ZstdSharp.dll", "lib/netstandard2.1/ZstdSharp.dll", "zstdsharp.port.0.7.3.nupkg.sha512", "zstdsharp.port.nuspec"]}, "PA.PAPA.DataAccess/1.0.0": {"type": "project", "path": "../PA.PAPA.DataAccess/PA.PAPA.DataAccess.csproj", "msbuildProject": "../PA.PAPA.DataAccess/PA.PAPA.DataAccess.csproj"}, "PA.PAPA.DTO/1.0.0": {"type": "project", "path": "../PA.PAPA.DTO/PA.PAPA.DTO.csproj", "msbuildProject": "../PA.PAPA.DTO/PA.PAPA.DTO.csproj"}, "PA.PAPA.Logic/1.0.0": {"type": "project", "path": "../PA.PAPA.Logic/PA.PAPA.Logic.csproj", "msbuildProject": "../PA.PAPA.Logic/PA.PAPA.Logic.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["FluentValidation.AspNetCore >= 11.3.0", "Microsoft.AspNetCore.Authentication.JwtBearer >= 8.0.2", "Microsoft.AspNetCore.Mvc.NewtonsoftJson >= 8.0.2", "Microsoft.Data.SqlClient >= 5.1.5", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets >= 1.19.5", "MongoDB.Driver >= 2.24.0", "PA.PAPA.DTO >= 1.0.0", "PA.PAPA.DataAccess >= 1.0.0", "PA.PAPA.Logic >= 1.0.0", "Serilog >= 4.2.0", "Serilog.AspNetCore >= 9.0.0", "Serilog.Enrichers.Environment >= 2.3.0", "Serilog.Extensions.Logging >= 9.0.0", "Serilog.Settings.Configuration >= 9.0.0", "Serilog.Sinks.Async >= 1.5.0", "Serilog.Sinks.File >= 6.0.0", "Serilog.Sinks.RollingFile >= 3.3.0", "Serilog.Sinks.Seq >= 6.0.0", "SmartIT.DebugTraceHelper >= 1.0.5", "Swashbuckle.AspNetCore >= 6.5.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Net9\\PAPA_API\\PA.PAPA.API\\PA.PAPA.API.csproj", "projectName": "PA.PAPA.API", "projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.API\\PA.PAPA.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Net9\\PAPA_API\\PA.PAPA.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"M:\\NugetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Net9\\PAPA_API\\PA.PAPA.DataAccess\\PA.PAPA.DataAccess.csproj": {"projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DataAccess\\PA.PAPA.DataAccess.csproj"}, "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj": {"projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.DTO\\PA.PAPA.DTO.csproj"}, "C:\\Net9\\PAPA_API\\PA.PAPA.Logic\\PA.PAPA.Logic.csproj": {"projectPath": "C:\\Net9\\PAPA_API\\PA.PAPA.Logic\\PA.PAPA.Logic.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.5, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.19.5, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.24.0, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[2.3.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[1.5.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.RollingFile": {"target": "Package", "version": "[3.3.0, )"}, "Serilog.Sinks.Seq": {"target": "Package", "version": "[6.0.0, )"}, "SmartIT.DebugTraceHelper": {"target": "Package", "version": "[1.0.5, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}}