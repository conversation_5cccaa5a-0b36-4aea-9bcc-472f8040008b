﻿using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using FluentValidation;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DTO.Admin;
using PA.PAPA.Logic.Admin.Interfaces;

namespace PA.PAPA.Logic.Admin
{
    public class EmployeeLogic : IEmployeeLogic
    {
        private readonly IEmployeeRepository _repo;

        public EmployeeLogic(IEmployeeRepository EmployeeRepository) {
            _repo = EmployeeRepository;
        }

        public async Task<IEnumerable<EmployeeDTO>> Read() {
            return await _repo.Read();
        }

        //public async Task<IList<EmployeeCompactDTO>> ReadCompactList(int? organizationId)
        //{
        //    return await _repo.ReadCompactList(organizationId);
        //}

        public async Task<EmployeeDTO> Read(int id) {
            return await _repo.Read(id);
        }

        public async Task<EmployeeDTO> Create(EmployeeDTO dto) {
            //TODO: Add data validation and other business logic
            Validate(dto);
            return await _repo.Create(dto);
        }

        public async Task<EmployeeDTO> Update(EmployeeDTO dto) {
            if (dto == null) {
                var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", "Employee is not set") };
                throw new FluentValidation.ValidationException("", errors);
            }

            var existing = await _repo.Read(dto.EmployeeId);
            if (existing == default(EmployeeDTO)) {
                var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", $"Employee id ({dto.EmployeeId}) not found") };
                throw new FluentValidation.ValidationException("", errors);
            }

            Validate(dto);

            try {
                return await _repo.Update(dto);
            }
            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    var errors = new List<FluentValidation.Results.ValidationFailure>() { new FluentValidation.Results.ValidationFailure("", ex.ToString()) };
                    throw new FluentValidation.ValidationException("", errors);
                }
                throw;
            }
            catch (Exception) {
                throw;
            }
        }

        //// TODO DELETE
        //public bool DuplicateExists(int organizationId, string EmployeeName)
        //{
        //    throw new NotImplementedException();
        //}

        public async Task<IEnumerable<EmployeeTypeDTO>> ReadEmployeeTypes() {
            return await _repo.ReadEmployeeTypes();
        }

        public void Validate(EmployeeDTO dto) {
            var validator = new EmployeeValidator();
            validator.ValidateAndThrow(dto);
        }

        public class EmployeeValidator : AbstractValidator<EmployeeDTO> {
            public EmployeeValidator() {
                RuleFor(x => x.FirstName).NotNull();
                RuleFor(x => x.FirstName).Length(0, 50).WithMessage("First Name must be less than 50 characters.");

                RuleFor(x => x.LastName).NotNull();
                RuleFor(x => x.LastName).Length(0, 50).WithMessage("Last Name must be less than 50 characters.");

                RuleFor(x => x.UserName).NotNull();
                RuleFor(x => x.UserName).Length(0, 15).WithMessage("User Name must be less than 15 characters.");
            }
        }

        public void Dispose() { }
    }
}