using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using PAPA_API.Services;
using PAPA_API.Models;
using Microsoft.Extensions.Logging;

namespace PAPA_API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, ILogger<UserController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(int id)
        {
            try
            {
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new ApiResponse<object>(false, "User not found", null));
                }
                return Ok(new ApiResponse<User>(true, "User retrieved successfully", user));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user");
                return StatusCode(500, new ApiResponse<object>(false, "An error occurred while retrieving the user", null));
            }
        }
    }
} 