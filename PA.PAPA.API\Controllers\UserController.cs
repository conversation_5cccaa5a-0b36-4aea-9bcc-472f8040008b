﻿using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace PA.PAPA.API.Controllers
{
    [Authorize]
    [ApiController]
    [Route("[controller]")]
    public class UserController : ControllerBase
    {
        private static readonly List<Employee> EmployeeList = new List<Employee>()
        {
            new Employee(){Id="1", EmployeeNumber= "100",Name = "John"},
            new Employee(){Id="2",EmployeeNumber="102",Name = "Wesley"},
            new Employee(){Id="3",EmployeeNumber="101",Name = "Mike"},


        };

        private readonly ILogger<UserController> _logger;

        public UserController(ILogger<UserController> logger)
        {
            _logger = logger;
            //var claims =  User.Claims.Select(c => new { type = c.Type, value = c.Value }).ToList();
            // var claims = User.Claims.ToList();
            // claims.DDump("User Claims");

        }

        [Authorize(Policy = "Member")]
        [HttpGet]
        public List<Employee> Get()
        {
            return EmployeeList;
        }

        //[Authorize(Policy = "Member")]
        [AllowAnonymous]
        [HttpGet("{userId:int}")]
        public Employee Get(string employeeNumber)
        {
            return EmployeeList.FirstOrDefault(e=>e.EmployeeNumber == employeeNumber);
        }
    }
}