﻿using FluentValidation;
using System.Net.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PA.PAPA.DTO;
using PA.PAPA.DTO.Core;
using PA.PAPA.DTO.Logging;
using PA.PAPA.Logic.Core.Interfaces;
using SmartIT.DebugHelper;
using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace PA.PAPA.API.Controllers
{
    [Route("api/[controller]")]
    //[Authorize]
    public class TimesheetController : Controller
    {
        private readonly ILogger<TimesheetController> _logger;
        private readonly ITimesheetLogic _logic;

        public TimesheetController(ITimesheetLogic TimesheetLogic, ILogger<TimesheetController> logger) {
            _logic = TimesheetLogic;
            _logger = logger;
        }

        [HttpGet]
        [Route("search")]
        public async Task<IActionResult> Get(DateTime startDate, int employeeId ) {
            try {
                var list = await _logic.Read(startDate, employeeId);
                if (list == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetListNumberOfRecordReturned200, list.Count());
                return Ok(list);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new ErrorResponse {
                        Messages = new List<Message>
                        {
                            new Message
                            {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(Events.Get, ex, "Error getting Timesheets: ", request.ConvertToJson());

                return request;
            }
        }

        [HttpGet("{id:int}")]
        public async Task<IActionResult> Get(int id) {
            try {
                var item = await _logic.Read(id);
                if (item == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetById200, item.ConvertToJson());
                return Ok(item);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new ErrorResponse {
                        Messages = new List<Message>
                        {
                            new Message
                            {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(ex, $"Error getting Timesheet Id: {id}", request);

                return request;
            }
        }

        //[HttpPost]
        //public async Task<IActionResult> Post([FromBody] IEnumerable<TimesheetDTO> dtos) {
        //    if (dtos == null) {
        //        var ErrorResponse = new ErrorResponse("Empty Payload", MessageType.Error);
        //        return BadRequest(ErrorResponse);
        //    }

        //    try {
        //        var entity = await _logic.Save(dtos);
        //        _logger.LogInformation(Events.Post, null, Messages.PostRecordCreated_201, entity.ConvertToJson());

        //        return Ok(entity);
        //    }
        //    catch (ValidationException ex) {
        //        var request = BadRequest(new ErrorResponse(ex.Errors.Select(a => (PropertyValidationError)a)));
        //        _logger.LogError(Events.Post, ex, "Error saving Timesheets: ", request.ConvertToJson());
        //        return request;
        //    }

        //    catch (SqlException ex) {
        //        if (ex.Number == 2601 || ex.Number == 2627) {
        //            // Violation in one on both...
        //            var ErrorResponse = new ErrorResponse("Timesheet already exists", MessageType.Error);
        //            ErrorResponse.Messages[0].Exception = $"{ex.Message}";
        //            _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dtos.ConvertToJson(), ErrorResponse.ConvertToJson());

        //            return BadRequest(ErrorResponse);
        //        }

        //        var error = new ErrorResponse("An error has occurred processing your request.", MessageType.Error);
        //        error.Messages[0].Exception = $"{ex.Message}";
        //        _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dtos.ConvertToJson(), error.ConvertToJson());

        //        return BadRequest(error);
        //    }

        //    catch (Exception ex) {
        //        var ErrorResponse = new ErrorResponse(
        //            "An error has occurred processing your request.",
        //            MessageType.Error);
        //        ErrorResponse.Messages[0].Exception = $"{ex.Message}";
        //        _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dtos.ConvertToJson());
        //        return BadRequest(ErrorResponse);
        //    }
        //}



        [HttpPost]
        public async Task<IActionResult> Post([FromBody] Dictionary<string, string> dto) {
            if (dto == null) {
                var ErrorResponse = new ErrorResponse("Empty Payload", MessageType.Error);
                return BadRequest(ErrorResponse);
            }

            try {
                var entity = await _logic.Create(dto);
                _logger.LogInformation(Events.Post, null, Messages.PostRecordCreated_201, entity.ConvertToJson());

                return Ok(entity);
            }
            catch (ValidationException ex) {
                var request = BadRequest(new ErrorResponse(ex.Errors.Select(a => (PropertyValidationError)a)));
                _logger.LogError(Events.Get, ex, "Error inserting Timesheet: ", request.ConvertToJson());
                return request;
            }
            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    // Violation in one on both...
                    var ErrorResponse = new ErrorResponse("Timesheet already exists", MessageType.Error);
                    ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                    _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson(), ErrorResponse.ConvertToJson());

                    return BadRequest(ErrorResponse);
                }

                var error = new ErrorResponse("An error has occurred processing your request.", MessageType.Error);
                error.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson(), error.ConvertToJson());

                return BadRequest(error);
            }
            catch (Exception ex) {
                var ErrorResponse = new ErrorResponse(
                    "An error has occurred processing your request.",
                    MessageType.Error);
                ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson(), ErrorResponse.ConvertToJson());
                return BadRequest(ErrorResponse);
            }
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] Dictionary<string, string> dto) {
            try {
                var entity = await _logic.Update(dto);
                _logger.LogInformation(Events.Put, Messages.PutRecordUpdated, dto.ConvertToJson());
                return Ok(entity);
            }
            catch (ValidationException ex) {
                var request = BadRequest(new ErrorResponse(ex.Errors.Select(a => (PropertyValidationError)a)));                
                _logger.LogError(Events.Put, ex, "Error updating Timesheet: ", request.ConvertToJson());
                return request;
            }
            catch (Exception ex) {
                var ErrorResponse = new ErrorResponse(
                    "An error has occurred processing your request.",
                    MessageType.Error);
                ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Put, ex, Messages.PutRecordUpdateError, dto.ConvertToJson());
                return BadRequest(ErrorResponse);
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<IActionResult> Delete(int id) {
            try {
                var entity = await _logic.Delete(id);
                _logger.LogInformation(Events.Delete, Messages.DeleteRecordError, entity);
                return Ok(entity);
            }
            catch (ValidationException ex) {
                var request = BadRequest(new ErrorResponse(ex.Errors.Select(a => (PropertyValidationError)a)));
                _logger.LogError(Events.Delete, ex, "Error deleting Timesheet: ", request.ConvertToJson());
                return request;
            }
            catch (Exception ex) {
                var ErrorResponse = new ErrorResponse(
                    "An error has occurred processing your request.",
                    MessageType.Error);
                ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Delete, ex, Messages.DeleteRecordError, id);
                return BadRequest(ErrorResponse);
            }
        }

        protected override void Dispose(bool disposing) {
            if (disposing) {
                _logic.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}