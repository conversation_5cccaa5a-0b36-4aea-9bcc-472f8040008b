
namespace PA.PAPA.DTO.Admin
{
    public class EmployeeDTO
    {
        public int EmployeeId { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string LastName { get; set; }
        public string UserName { get; set; }
        public string EmployeeNumber { get; set; }
        public string EmailAddress { get; set; }
        public int EmployeeTypeId { get; set; }
        public int CompanySegmentId { get; set; }
        public int SiteSegmentId { get; set; }
        public int FunctionSegmentId { get; set; }
        public string ContractAgency { get; set; }
        public int CreatedById { get; set; }
        public string CreatedDate { get; set; }
        public int UpdatedById { get; set; }
        public string UpdatedDate { get; set; }
        public bool IsActive { get; set; }
        public string LastLoginDate { get; set; }
    }

}

