{
  "AAD": {
    "ResourceId": "api://fbf12875-107e-42ac-a536-586dd1d79ff4",
    "Instance": "https://login.microsoftonline.com/",
    "TenantId": "294c0f4e-49a4-4ad2-8465-ebd5de95f986"

  },
  "AllowedHosts": "*",
  "Logging": {
    "IncludeScopes": false,
    "Debug": {
      "LogLevel": {
        "Default": "Warning"
      }
    },
    "Console": {
      "LogLevel": {
        "Default": "Warning"
      }
    }
  },
  "ApiConfiguration": {
    //"DbConnectionString": "Server=***************;Database=Pals_TE;User e; password=admin78!" // **************
    //"DbConnectionString": "Server=CASC9MVDD01;Database=Papa;User Id=sql_papa_external_service_d; password=*************"


    //"ReportServiceUrl": "http://testapi.portsamerica.com/palsd1/svcrpts/v1/",
    //"FinancialServiceUrl": "http://testapi.portsamerica.com/pa/svcfinapp/v1/",
    //"EmailServiceUrl": "http://testapi.portsamerica.com/pa/svcemail/v1/",
    //"ClientId": "59E65AC2-B014-47DE-B681-65A5F3F3510D",
    //"SupportEmail": "nolasvcs@p ortsamerica.com"
  },

  "UserDatabaseSettings": {
    "UserCollectionName": "Employees",
    "ConnectionString": "mongodb://localhost:27017",
    "DatabaseName": "PapaSecurityDb"
  },
  "UserClaimDatabaseSettings": {
    "UserClaimsCollectionName": "UserClaims",
    "ConnectionString": "mongodb://localhost:27017",
    "DatabaseName": "PapaSecurityDb"
  },
  "RoleDatabaseSettings": {
    "RoleCollectionName": "Roles",
    "ConnectionString": "mongodb://localhost:27017",
    "DatabaseName": "PapaSecurityDb"
  },

  //"Tokens": {
  //  "Key": "1vdLFZXIrPIqKBLz7o2gMw2DwvO0EwrB",
  //  "Issuer": "http://localhost:60608",
  //  "Audience": "PapaUsers"
  //},
  "Serilog": {
    "Using": [ "Serilog.Sinks.RollingFile", "Serilog.Sinks.Async" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "RollingFile",
              "Args": { "pathFormat": "Logs/log-{Date}.log" }
            }
          ]
        }
      }
    ],
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId" ],
    "Properties": {
      "Application": "PAPA_API_V1"
    }
  }
}