<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beer OCR Application</title>
    <link rel="stylesheet" href="mainForm.css">
    <script>
        // Immediate script to ensure button functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded event fired in inline script');
            
            // Fix for upload button
            setTimeout(function() {
                const uploadButton = document.getElementById('uploadButton');
                const fileInput = document.getElementById('fileInput');
                
                if (uploadButton && fileInput) {
                    console.log('Setting up failsafe button handler');
                    
                    // Direct attachment to ensure it works
                    uploadButton.onclick = function(e) {
                        console.log('Upload button clicked via inline script');
                        if (fileInput) {
                            fileInput.click();
                        }
                        return false;
                    };
                }
            }, 500); // Small delay to ensure all other scripts have run
        });
    </script>
</head>
<body>
    <div class="app-container">
        <!-- App Header with Menu -->
        <header class="app-header">
            <nav class="main-menu">                <ul>
                    <li><a href="#">File</a>
                        <ul class="submenu">
                            <!--<li><a href="#" id="openFileMenuItem">Upload Photo</a></li>-->
                            <!--<li><a href="#" id="exitMenuItem">Exit</a></li>-->
                        </ul>
                    </li>
                    <li><a href="#">Help</a>
                        <ul class="submenu">
                            <!--<li><a href="#" id="aboutMenuItem">About</a></li>-->
                        </ul>
                    </li>
                </ul>
            </nav>
        </header>      <hr />        <!-- Main Toolbar - Equivalent to the top buttons in WinForms -->        <div class="toolbar">            <!-- Upload Button - Equivalent to "Upload Photo" in WinForms -->
            <button id="uploadButton" class="primary-button" type="button">
                <span class="button-text">Upload Photo</span>
            </button>
            
            <!-- Hidden file input for image upload -->
            <input type="file" id="fileInput" accept=".jpg,.jpeg,.png,.bmp" style="display: none;">
            
            <!-- Progress Bar - Equivalent to ProgressBar in WinForms --><div id="progressBar" class="progress-bar">
                <div class="progress-indicator"></div>
            </div>
           
        </div>        <!-- Status Area - Equivalent to statusLabel in WinForms -->
        <div id="statusLabel" class="status-label">
            Click "Upload Photo" button to upload a beer menu photo.
        </div>

        <!-- OCR Prompt Configuration -->
        <div class="ocr-config-section">
            <h3>OCR Prompt Configuration</h3>
            <textarea id="ocrPromptInput" class="ocr-prompt-input" rows="10">You are an assistant that formats **German drink menus** extracted via OCR. Your task is to:  
1. **Extract drink names and their prices** from the provided text.  
2. **Ignore volume information** (e.g., "cl", "ml", "L", "0,3L").  
3. **Standardize prices**:  
   - Convert `X,XX€` or `je X,XX` to `X.XX` (e.g., "5,90€" → "5.90").  
   - If no currency symbol is present, assume prices are in **Euros** (e.g., "3,50" → "3.50").  
4. **Output format**:  
   - Each line should contain: `[Drink Name] - [Price]€`  
   - Skip incomplete or fragmented entries (e.g., missing prices or names).

**Example Input:**</textarea>
            
            <h4>User Message</h4>
            <textarea id="userTextInput" class="user-text-input"  rows="1">Analyze the following German drink menu image.</textarea>
            <br />
            <button id="savePromptButton" class="secondary-button">
                <span class="button-text">Save Settings</span>
            </button>
        </div>

        <!-- Results Grid - Equivalent to resultsGrid in WinForms -->
        <div class="grid-container">
            <table id="resultsGrid" class="results-grid">                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Price</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Results will be added here dynamically -->
                </tbody>
            </table>
        </div>        <!-- Footer - Additional element for web version -->
        <footer class="app-footer">
            <p>© 2025 - NT.BIRAOCR Web Application</p>
        </footer>
    </div>

    <!-- Modal for "About" dialog -->
    <div id="aboutModal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>            <h2>About Beer OCR Application</h2>
            <p>This application uses OCR technology to extract product names and prices from beer menu photos.</p>
            <p>Version: 1.0</p>
            <p>© 2025 - All rights reserved.</p>
        </div>
    </div>

    <!-- Modal for displaying processing messages -->
    <div id="processingModal" class="modal processing-modal">
        <div class="modal-content processing-content">
            <div class="spinner"></div>
            <p id="processingMessage">Processing... Please wait.</p>
        </div>
    </div>

    <script src="mainForm.js"></script>
</body>
</html>