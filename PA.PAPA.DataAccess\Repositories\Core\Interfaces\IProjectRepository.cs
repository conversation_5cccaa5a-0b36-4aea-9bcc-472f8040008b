﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PA.PAPA.DTO.Core;

namespace PA.PAPA.DataAccess.Repositories.Core.Interfaces
{
    public interface IProjectRepository : IDisposable
    {        
        Task<ProjectDTO> Read(int id);
        Task<ProjectDTO> Create(ProjectDTO dto);
        Task<ProjectDTO> Update(ProjectDTO dto);


        Task<IEnumerable<ProjectDTO>> ReadProjects();
        Task<IEnumerable<ProjectActivityDTO>> ReadProjectActivities();
        Task<IEnumerable<ProjectDTO>> ReadEmployeeProjects(int employeeId);
        Task<IEnumerable<ProjectEmployeeActivityDTO>> ReadEmployeeProjectActivities(int employeeId);

    }
}