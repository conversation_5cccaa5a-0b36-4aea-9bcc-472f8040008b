﻿using System.Collections.Generic;
using BooksApi.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PA.PAPA.API.Services;

namespace PA.PAPA.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RoleController : ControllerBase
    {
        private readonly RoleService _roleService;

        public RoleController(RoleService roleService)
        {
            _roleService = roleService;
        }

        [HttpGet]
        public ActionResult<List<Role>> Get() =>
            _roleService.Get();

        //[AllowAnonymous]
        //[HttpGet("{userId:int}")]
        //public ActionResult<List<Role>> Get(int userId) =>
        //    _roleService.Get(userId);

        //[HttpGet("{id:length(24)}", Name = "GetRole")]
        //public ActionResult<Role> Get(string id)
        //{
        //    var userClaim = _roleService.Get(id);

        //    if (userClaim == null)
        //    {
        //        return NotFound();
        //    }

        //    return userClaim;
        //}

        [HttpPost]
        public ActionResult<Role> Post(Role item)
        {
            _roleService.Create(item);
            return Ok(item);
            //return CreatedAtRoute("Post", new { id = role.Id.ToString() }, role);
        }

        [HttpPut("{id:length(24)}")]
        public IActionResult Put(string id, Role item)
        {
            var role = _roleService.Get(id);

            if (role == null)
            {
                return NotFound();
            }

            _roleService.Update(id, item);

            return NoContent();
        }

        [HttpDelete("{id:length(24)}")]
        public IActionResult Delete(string id)
        {
            var role = _roleService.Get(id);

            if (role == null)
            {
                return NotFound();
            }

            _roleService.Remove(role.Id);

            return NoContent();
        }
    }
}
