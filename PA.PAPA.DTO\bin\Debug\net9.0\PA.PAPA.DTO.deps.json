{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"PA.PAPA.DTO/1.0.0": {"dependencies": {"FluentValidation.AspNetCore": "11.3.0"}, "runtime": {"PA.PAPA.DTO.dll": {}}}, "FluentValidation/11.5.1": {"runtime": {"lib/net7.0/FluentValidation.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.5.1.0"}}}, "FluentValidation.AspNetCore/11.3.0": {"dependencies": {"FluentValidation": "11.5.1", "FluentValidation.DependencyInjectionExtensions": "11.5.1"}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.3.0.0"}}}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"dependencies": {"FluentValidation": "11.5.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.5.1.0"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.0": {}}}, "libraries": {"PA.PAPA.DTO/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FluentValidation/11.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-0h1Q5lNOLLyYTWMJmyNoMqhY4CBRvvUWvJP1R4F2CnmmzuWwvB0A8aVmw5+lOuwYnwUwCRrdeMLbc81F38ahNQ==", "path": "fluentvalidation/11.5.1", "hashPath": "fluentvalidation.11.5.1.nupkg.sha512"}, "FluentValidation.AspNetCore/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtFVgKnDFySyBlPS8bZbTKEEwJZnn11rXXJ2SQnjDhZ56rQqybBg9Joq4crRLz3y0QR8WoOq4iE4piV81w/Djg==", "path": "fluentvalidation.aspnetcore/11.3.0", "hashPath": "fluentvalidation.aspnetcore.11.3.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-iWM0LS1MDYX06pcjMEQKqHirl2zkjHlNV23mEJSoR1IZI7KQmTa0RcTtGEJpj5+iHvBCfrzP2mYKM4FtRKVb+A==", "path": "fluentvalidation.dependencyinjectionextensions/11.5.1", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.5.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-8/CtASu80UIoyG+r8FstrmZW5GLtXxzoYpjj3jV0FKZCL5CiFgSH3pAmqut/dC68mu7N1bU6v0UtKKL3gCUQGQ==", "path": "microsoft.extensions.dependencyinjection.abstractions/2.1.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.2.1.0.nupkg.sha512"}}}