﻿/*
 * © 2025 <PERSON> | SmartIT. All Rights Reserved.
 * https://johnkocer.github.io
 * Version: 1.0.0 Date: 05-21-2025
 */

using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.Net.Http.Headers;
using System.Diagnostics;
using NT.BIRAOCR.DTO;
using NT.BIRAOCR.Logic.Interfaces;

namespace NT.BIRAOCR.API.Controllers
{
  [ApiController]
  [Route("api/[controller]")]
  public class AuthController : ControllerBase
  {
    private readonly IUserService _userService;

    public AuthController(IUserService userService)
    {
      _userService = userService;
    }

    [HttpPost("register")]
    public async Task<IActionResult> Register([FromBody] UserRegistrationDto registration)
    {
      try
      {
        var result = await _userService.RegisterAsync(registration);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(new { error = ex.Message });
      }
    }
    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] UserLoginDto login)
    {
      try
      {
        var result = await _userService.LoginAsync(login);
        return Ok(result);
      }
      catch (Exception ex)
      {
        return BadRequest(new { error = ex.Message });
      }
    }

    [HttpPost("validate-token")]
    public async Task<IActionResult> ValidateToken([FromBody] string token)
    {
      try
      {
        var isValid = await _userService.ValidateTokenAsync(token);
        return Ok(new { isValid = isValid });
      }
      catch (Exception ex)
      {
        return BadRequest(new { error = ex.Message });
      }
    }
  }
}