﻿using System;
using Newtonsoft.Json;

namespace PA.PAPA.API.Converters
{
	public class DateTimeFormatConverter : JsonConverter
	{
		public override bool CanConvert(Type objectType)
		{
			return (objectType == typeof(DateTime));
		}

		public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
		{
			return existingValue;
		}

		public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
		{
			if (writer.Path.EndsWith("createdDate") || writer.Path.EndsWith("updatedDate")) {
				var dtoValue = (DateTime)value;
				writer.WriteValue(dtoValue.ToUniversalTime());
			}
			else if (writer.Path.EndsWith("workDate")) { // | writer.Path.EndsWith("startDate") | writer.Path.EndsWith("endDate")) {
				var dtoValue = (DateTime)value;
				writer.WriteValue(dtoValue.ToString("MM/dd/yyyy"));
			}
			else if (writer.Path.EndsWith("payPeriodStartLocal")) {
				var dtoValue = (DateTime)value;
				writer.WriteValue(dtoValue.ToString("MM/dd/yyyy HH:mm"));
			}
			else if (writer.Path.EndsWith("payPeriodStart")) {
				var dtoValue = (DateTime)value;
				writer.WriteValue(dtoValue.ToString("MM/dd/yyyy HH:mm"));
			}
			else {
				var dtoValue = (DateTime)value;
				writer.WriteValue(dtoValue.ToString("MM/dd/yyyy hh:mm"));
			}
		}

		public override bool CanRead {
			get { return false; }
		}
	}
}