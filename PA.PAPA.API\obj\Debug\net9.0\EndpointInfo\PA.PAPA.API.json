{"openapi": "3.0.1", "info": {"title": "PAPA API", "version": "v1"}, "paths": {"/api/Activity": {"get": {"tags": ["Activity"], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["Activity"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ActivityDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ActivityDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActivityDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ActivityDTO"}}}}, "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Activity"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ActivityDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ActivityDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActivityDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ActivityDTO"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Activity/{id}": {"get": {"tags": ["Activity"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Employee": {"get": {"tags": ["Employee"], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["Employee"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmployeeDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmployeeDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeDTO"}}}}, "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Employee"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmployeeDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmployeeDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeDTO"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Employee/{id}": {"get": {"tags": ["Employee"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Employee/types": {"get": {"tags": ["Employee"], "responses": {"200": {"description": "Success"}}}}, "/api/EmployeeProxy/search": {"get": {"tags": ["EmployeeProxy"], "parameters": [{"name": "parentEmployeeId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/EmployeeProxy/{id}": {"get": {"tags": ["EmployeeProxy"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/EmployeeProxy": {"post": {"tags": ["EmployeeProxy"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmployeeProxyDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmployeeProxyDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeProxyDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeProxyDTO"}}}}, "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["EmployeeProxy"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/EmployeeProxyDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EmployeeProxyDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmployeeProxyDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmployeeProxyDTO"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Project/{id}": {"get": {"tags": ["Project"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Project": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ProjectDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDTO"}}}}, "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Project"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ProjectDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectDTO"}}}}, "responses": {"200": {"description": "Success"}}}, "get": {"tags": ["Project"], "responses": {"200": {"description": "Success"}}}}, "/api/Project/activities": {"get": {"tags": ["Project"], "responses": {"200": {"description": "Success"}}}}, "/api/Project/activities/employee/{employeeId}": {"get": {"tags": ["Project"], "parameters": [{"name": "employeeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Project/employee/{employeeId}": {"get": {"tags": ["Project"], "parameters": [{"name": "employeeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Role": {"get": {"tags": ["Role"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}}}}}, "post": {"tags": ["Role"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Role"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Role"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Role"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Role"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Role"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Role"}}}}}}}, "/api/Role/{id}": {"put": {"tags": ["Role"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Role"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Role"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Role"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Role"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Service": {"get": {"tags": ["Service"], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["Service"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ServiceDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ServiceDTO"}}}}, "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Service"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ServiceDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ServiceDTO"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Service/{id}": {"get": {"tags": ["Service"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Service"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Timesheet/search": {"get": {"tags": ["Timesheet"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "employeeId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Timesheet/{id}": {"get": {"tags": ["Timesheet"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Timesheet"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Timesheet": {"post": {"tags": ["Timesheet"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Timesheet"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/User": {"get": {"tags": ["User"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}}}}}}}}, "/User/{userId}": {"get": {"tags": ["User"], "parameters": [{"name": "employeeNumber", "in": "query", "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Employee"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Employee"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Employee"}}}}}}}, "/api/UserClaims": {"get": {"tags": ["UserClaims"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserClaim"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserClaim"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserClaim"}}}}}}}, "post": {"tags": ["UserClaims"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}}}}}}, "/api/UserClaims/{userId}": {"get": {"tags": ["UserClaims"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserClaim"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserClaim"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserClaim"}}}}}}}}, "/api/UserClaims/{id}": {"get": {"tags": ["UserClaims"], "operationId": "GetUserClaim", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}}}}}, "put": {"tags": ["UserClaims"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserClaim"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["UserClaims"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}}}}}}}, "post": {"tags": ["Users"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Employee"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Employee"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Employee"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Employee"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/User"}}, "application/json": {"schema": {"$ref": "#/components/schemas/User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/api/Users/<USER>": {"get": {"tags": ["Users"], "operationId": "GetUser", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Employee"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Employee"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Employee"}}}}}}, "put": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Employee"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Employee"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Employee"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Employee"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"ActivityDTO": {"type": "object", "properties": {"activityId": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "isCapEx": {"type": "boolean"}, "createdById": {"type": "integer", "format": "int32"}, "createdDate": {"type": "string", "format": "date-time"}, "updatedById": {"type": "integer", "format": "int32"}, "updatedDate": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "Employee": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "employeeNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EmployeeDTO": {"type": "object", "properties": {"employeeId": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "middleName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "employeeNumber": {"type": "string", "nullable": true}, "emailAddress": {"type": "string", "nullable": true}, "employeeTypeId": {"type": "integer", "format": "int32"}, "companySegmentId": {"type": "integer", "format": "int32"}, "siteSegmentId": {"type": "integer", "format": "int32"}, "functionSegmentId": {"type": "integer", "format": "int32"}, "contractAgency": {"type": "string", "nullable": true}, "createdById": {"type": "integer", "format": "int32"}, "createdDate": {"type": "string", "nullable": true}, "updatedById": {"type": "integer", "format": "int32"}, "updatedDate": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "lastLoginDate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EmployeeProxyDTO": {"type": "object", "properties": {"employeeProxyId": {"type": "integer", "format": "int32"}, "employeeId": {"type": "integer", "format": "int32"}, "parentEmployeeId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProjectDTO": {"type": "object", "properties": {"projectId": {"type": "integer", "format": "int32"}, "projectName": {"type": "string", "nullable": true}, "projectTypeId": {"type": "integer", "format": "int32"}, "ownerEmployeeId": {"type": "integer", "format": "int32"}, "projectStatusId": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "companySegmentId": {"type": "integer", "format": "int32"}, "siteSegmentId": {"type": "integer", "format": "int32"}, "functionSegmentId": {"type": "integer", "format": "int32"}, "applicationId": {"type": "integer", "format": "int32"}, "afe": {"type": "string", "nullable": true}, "afeAmount": {"type": "string", "nullable": true}, "capitalLaborProject": {"type": "boolean"}, "externalSpend": {"type": "boolean"}, "budgeted": {"type": "boolean"}, "createdById": {"type": "integer", "format": "int32"}, "createdDate": {"type": "string", "format": "date-time"}, "updatedById": {"type": "integer", "format": "int32"}, "updatedDate": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "Role": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "description": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "normalizedName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ServiceDTO": {"type": "object", "properties": {"serviceId": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdById": {"type": "integer", "format": "int32"}, "createdDate": {"type": "string", "format": "date-time"}, "updatedById": {"type": "integer", "format": "int32"}, "updatedDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "User": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "employeeNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserClaim": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "claimId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "claimType": {"type": "string", "nullable": true}, "claimValue": {"type": "string", "nullable": true}, "employeeNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}