[{"ContainingType": "PA.PAPA.API.Controllers.ActivityController", "Method": "Get", "RelativePath": "api/Activity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ActivityController", "Method": "Post", "RelativePath": "api/Activity", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PA.PAPA.DTO.Admin.ActivityDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ActivityController", "Method": "Put", "RelativePath": "api/Activity", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PA.PAPA.DTO.Admin.ActivityDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ActivityController", "Method": "Get", "RelativePath": "api/Activity/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.EmployeeController", "Method": "Get", "RelativePath": "api/Employee", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.EmployeeController", "Method": "Post", "RelativePath": "api/Employee", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PA.PAPA.DTO.Admin.EmployeeDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.EmployeeController", "Method": "Put", "RelativePath": "api/Employee", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PA.PAPA.DTO.Admin.EmployeeDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.EmployeeController", "Method": "Get", "RelativePath": "api/Employee/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.EmployeeController", "Method": "GetEmployeeTypes", "RelativePath": "api/Employee/types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.EmployeeProxyController", "Method": "Post", "RelativePath": "api/EmployeeProxy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PA.PAPA.DTO.Admin.EmployeeProxyDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.EmployeeProxyController", "Method": "Put", "RelativePath": "api/EmployeeProxy", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PA.PAPA.DTO.Admin.EmployeeProxyDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.EmployeeProxyController", "Method": "Get", "RelativePath": "api/EmployeeProxy/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.EmployeeProxyController", "Method": "GetByParent", "RelativePath": "api/EmployeeProxy/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentEmployeeId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ProjectController", "Method": "Post", "RelativePath": "api/Project", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PA.PAPA.DTO.Core.ProjectDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ProjectController", "Method": "Put", "RelativePath": "api/Project", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PA.PAPA.DTO.Core.ProjectDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ProjectController", "Method": "GetProjects", "RelativePath": "api/Project", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ProjectController", "Method": "Get", "RelativePath": "api/Project/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ProjectController", "Method": "GetProjectActivities", "RelativePath": "api/Project/activities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ProjectController", "Method": "GetEmployeeProjectActivities", "RelativePath": "api/Project/activities/employee/{employeeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "employeeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ProjectController", "Method": "GetEmployeeProjects", "RelativePath": "api/Project/employee/{employeeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "employeeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.RoleController", "Method": "Get", "RelativePath": "api/Role", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BooksApi.Models.Role, PA.PAPA.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PA.PAPA.API.Controllers.RoleController", "Method": "Post", "RelativePath": "api/Role", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "item", "Type": "BooksApi.Models.Role", "IsRequired": true}], "ReturnTypes": [{"Type": "BooksApi.Models.Role", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PA.PAPA.API.Controllers.RoleController", "Method": "Put", "RelativePath": "api/Role/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "item", "Type": "BooksApi.Models.Role", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.RoleController", "Method": "Delete", "RelativePath": "api/Role/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ServiceController", "Method": "Get", "RelativePath": "api/Service", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ServiceController", "Method": "Post", "RelativePath": "api/Service", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PA.PAPA.DTO.Admin.ServiceDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ServiceController", "Method": "Put", "RelativePath": "api/Service", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "PA.PAPA.DTO.Admin.ServiceDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ServiceController", "Method": "Get", "RelativePath": "api/Service/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.ServiceController", "Method": "Delete", "RelativePath": "api/Service/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.TimesheetController", "Method": "Post", "RelativePath": "api/Timesheet", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.TimesheetController", "Method": "Put", "RelativePath": "api/Timesheet", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.TimesheetController", "Method": "Get", "RelativePath": "api/Timesheet/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.TimesheetController", "Method": "Delete", "RelativePath": "api/Timesheet/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.TimesheetController", "Method": "Get", "RelativePath": "api/Timesheet/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "employeeId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.UserClaimsController", "Method": "Get", "RelativePath": "api/UserClaims", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BooksApi.Models.UserClaim, PA.PAPA.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PA.PAPA.API.Controllers.UserClaimsController", "Method": "Create", "RelativePath": "api/UserClaims", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userClaim", "Type": "BooksApi.Models.UserClaim", "IsRequired": true}], "ReturnTypes": [{"Type": "BooksApi.Models.UserClaim", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PA.PAPA.API.Controllers.UserClaimsController", "Method": "Get", "RelativePath": "api/UserClaims/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "BooksApi.Models.UserClaim", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetUserClaim"}, {"ContainingType": "PA.PAPA.API.Controllers.UserClaimsController", "Method": "Update", "RelativePath": "api/UserClaims/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "userClaimIn", "Type": "BooksApi.Models.UserClaim", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.UserClaimsController", "Method": "Delete", "RelativePath": "api/UserClaims/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.UserClaimsController", "Method": "Get", "RelativePath": "api/UserClaims/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BooksApi.Models.UserClaim, PA.PAPA.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PA.PAPA.API.Controllers.UsersController", "Method": "Get", "RelativePath": "api/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Employee, PA.PAPA.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PA.PAPA.API.Controllers.UsersController", "Method": "Create", "RelativePath": "api/Users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "Employee", "IsRequired": true}], "ReturnTypes": [{"Type": "PA.PAPA.API.Models.User", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PA.PAPA.API.Controllers.UsersController", "Method": "Get", "RelativePath": "api/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Employee", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetUser"}, {"ContainingType": "PA.PAPA.API.Controllers.UsersController", "Method": "Update", "RelativePath": "api/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "userIn", "Type": "Employee", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.UsersController", "Method": "Delete", "RelativePath": "api/Users/<USER>", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "PA.PAPA.API.Controllers.UserController", "Method": "Get", "RelativePath": "User", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[Employee, PA.PAPA.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PA.PAPA.API.Controllers.UserController", "Method": "Get", "RelativePath": "User/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "employeeNumber", "Type": "System.String", "IsRequired": false}, {"Name": "userId", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "Employee", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "PA.PAPA.API.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SecureAPI.WeatherForecast, PA.PAPA.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]