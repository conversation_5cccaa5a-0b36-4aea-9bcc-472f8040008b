﻿using System;
using Newtonsoft.Json;

namespace PA.PAPA.API.Converters
{
	public class TimeSpanFormatConverter : JsonConverter
	{
		public override bool CanConvert(Type objectType)
		{
			return (objectType == typeof(TimeSpan?));
		}

		public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
		{
			return existingValue;
		}

		public override void Write<PERSON><PERSON>(JsonWriter writer, object value, JsonSerializer serializer)
		{
            if (writer.Path.EndsWith("startTime") || writer.Path.EndsWith("endTime"))
            {

                if (value == null)
                               {
                    writer.WriteValue(string.Empty);
                }
                else
                {
                    var dtoValue = (TimeSpan)value;
                    writer.WriteValue(dtoValue.ToString(@"hh\:mm"));
                }
            }
            else
            {
                var dtoValue = (TimeSpan)value;
                writer.WriteValue(dtoValue.ToString(@"hh\:mm\:ss"));
            }
        }

		public override bool CanRead {
			get { return false; }
		}
	}
}