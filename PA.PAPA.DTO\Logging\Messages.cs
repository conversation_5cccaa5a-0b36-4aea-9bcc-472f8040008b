namespace PA.PAPA.DTO.Logging
{
    public class Messages
    {
        public const string GetListNumberOfRecordReturned200 = "Number of record returned {counts}";
        public const string GetById200 = "Record returned: {entity}";

        public const string PostRecordCreated_201 = "Record created : {entity}";
        public const string PostRecordCreateError = "Entity Create Error:  {entity} : BaseResponse: {response}";
        
        public const string PutRecordUpdated = "Record Updated: {entity}";
        public const string PutRecordUpdateError = "Entity update Error : {entity} : BaseResponse: {response}";

        public const string DeleteRecordDeleted_204 = "Record Id: {id})";
        public const string DeleteRecordError = "Entity Delete Error Id: {id})  : BaseResponse: {response}";
    }
}