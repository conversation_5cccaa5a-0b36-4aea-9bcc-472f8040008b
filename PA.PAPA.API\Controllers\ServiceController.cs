﻿using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PA.PAPA.DTO;
using PA.PAPA.DTO.Admin;
using PA.PAPA.DTO.Logging;
using SmartIT.DebugHelper;
using FluentValidation;
using PA.PAPA.Logic.Admin.Interfaces;

namespace PA.PAPA.API.Controllers
{
    [Route("api/[controller]")]
    public class ServiceController : Controller
    {
        private readonly ILogger<ServiceController> _logger;
        private readonly IServiceLogic _logic;

        public ServiceController(IServiceLogic serviceLogic, ILogger<ServiceController> logger)
        {
            _logic = serviceLogic;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try {
                var list = await _logic.Read();
                if (list == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetListNumberOfRecordReturned200, list.Count);
                return Ok(list);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new ErrorResponse {
                        Messages = new List<Message>
                        {
                            new Message
                            {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(Events.Get, ex, "Error getting Services: ", request.ConvertToJson());

                return request;
            }
        }

        [HttpGet("{id:int}")]
        public async Task<IActionResult> Get(int id)
        {
            try {
                var item = await _logic.Read(id);
                if (item == null) return NotFound();
                _logger.LogInformation(Events.Get, Messages.GetById200, item.ConvertToJson());
                return Ok(item);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new ErrorResponse {
                        Messages = new List<Message>
                        {
                            new Message
                            {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(ex, $"Error getting Service Id: {id}", request);

                return request;
            }
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody] ServiceDTO dto)
        {
            if (dto == null) {
                var ErrorResponse = new ErrorResponse("Empty Payload", MessageType.Error);
                return BadRequest(ErrorResponse);
            }

            try {
                var entity = await _logic.Create(dto);
                _logger.LogInformation(Events.Post, null, Messages.PostRecordCreated_201, entity.ConvertToJson());

                return Ok(entity);
            }
            catch (ValidationException ex) {
                var request = BadRequest(new ErrorResponse(ex.Errors.Select(a => (PropertyValidationError)a)));
                _logger.LogError(Events.Get, ex, "Error getting Services: ", request.ConvertToJson());
                return request;
            }
            catch (SqlException ex) {
                if (ex.Number == 2601 || ex.Number == 2627) {
                    // Violation in one on both...
                    var ErrorResponse = new ErrorResponse("Service already exists", MessageType.Error);
                    ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                    _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson(), ErrorResponse.ConvertToJson());

                    return BadRequest(ErrorResponse);
                }

                var error = new ErrorResponse("An error has occurred processing your request.", MessageType.Error);
                error.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson(), error.ConvertToJson());

                return BadRequest(error);
            }
            catch (Exception ex) {
                var ErrorResponse = new ErrorResponse(
                    "An error has occurred processing your request.",
                    MessageType.Error);
                ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Post, ex, Messages.PostRecordCreateError, dto.ConvertToJson());
                return BadRequest(ErrorResponse);
            }
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] ServiceDTO dto)
        {
            try {
                var entity = await _logic.Update(dto);
                _logger.LogInformation(Events.Put, Messages.PutRecordUpdated, dto);
                return Ok(entity);
            }
            catch (ValidationException ex) {
                var request = BadRequest(new ErrorResponse(ex.Errors.Select(a => (PropertyValidationError)a)));
                _logger.LogError(Events.Put, ex, "Error getting Services: ", request.ConvertToJson());
                return request;
            }
            catch (Exception ex) {
                var ErrorResponse = new ErrorResponse(
                    "An error has occurred processing your request.",
                    MessageType.Error);
                ErrorResponse.Messages[0].Exception = $"{ex.Message}";
                _logger.LogError(Events.Put, ex, Messages.PutRecordUpdateError, dto.ConvertToJson());

                return BadRequest(ErrorResponse);
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<IActionResult> Delete(int id)
        {
            try {
                var item = await _logic.Delete(id);
                if (item == null) return NotFound();
                _logger.LogInformation(Events.Delete, Messages.DeleteRecordError, item.ConvertToJson());
                return Ok(item);
            }
            catch (Exception ex) {
                var request = BadRequest(
                    new ErrorResponse {
                        Messages = new List<Message>
                        {
                            new Message
                            {
                                Type = MessageType.Error,
                                Text = "An error has occurred processing your request. ",
                                Exception = $"Exception:{ex.Message} "
                            }
                        }
                    });
                _logger.LogError(ex, $"Error deleting Service Id: {id}", request);

                return request;
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing) {
                _logic.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
