﻿using System.Collections.Generic;

namespace PA.PAPA.DTO
{
    public interface IErrorResponse
    {
        void Add(string message, MessageType type);

        void Add(Message message);

        void Add(PropertyValidationError propertyValidationError);
    }

    public class ErrorResponse : IErrorResponse
    {
        public List<PropertyValidationError> ValidationErrors = new List<PropertyValidationError>();
        public List<Message> Messages = new List<Message>();

        public ErrorResponse() {
        }

        public ErrorResponse(string message, MessageType type) {
            Messages.Add(new Message { Text = message, Type = type });
        }

        public ErrorResponse(IEnumerable<PropertyValidationError> propertyValidationErrors) {
            this.ValidationErrors.AddRange(propertyValidationErrors);
        }

        public ErrorResponse(List<Message> messages, IEnumerable<PropertyValidationError> propertyValidationErrors) {
            Messages = messages;
            this.ValidationErrors.AddRange(propertyValidationErrors);
        }

        //public bool Success => !Messages.Exists(e =>
        //{
        //    return e.Type == MessageType.Error || e.Type == MessageType.ValidationError ||
        //           e.Type == MessageType.ConcurrencyError;
        //});

        public void Add(string message, MessageType type) {
            Messages.Add(new Message { Text = message, Type = type });
        }

        public void Add(Message message) {
            Messages.Add(message);
        }

        public void Add(PropertyValidationError propertyValidationError) {
            this.ValidationErrors.Add(propertyValidationError);
        }

    }
}