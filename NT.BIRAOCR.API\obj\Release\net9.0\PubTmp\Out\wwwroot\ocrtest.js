/*
 * © 2025 <PERSON> | SmartIT. All Rights Reserved.
 * https://johnkocer.github.io
 * Version: 1.0.0 Date: 05-21-2025
 *
 * mainForm.js - Web version functionality for BeerOCRApp
 */

document.addEventListener('DOMContentLoaded', () => {
    // Initialize UI components and event handlers
    console.log('DOM Content Loaded - Initializing application');
    initializeApp();
});

// Global variables to store the OCR prompt and user text
let ocrPrompt;
let userText;

// Main application initialization
function initializeApp() {
    // Button event handlers
    const uploadButton = document.getElementById('uploadButton');
    const exitButton = document.getElementById('exitButton');
    const fileInput = document.getElementById('fileInput');
    const savePromptButton = document.getElementById('savePromptButton');
    const ocrPromptInput = document.getElementById('ocrPromptInput');
    const userTextInput = document.getElementById('userTextInput');
    
    // Debug element existence
    console.log('Upload button exists:', !!uploadButton);
    console.log('File input exists:', !!fileInput);
    console.log('Save prompt button exists:', !!savePromptButton);
    
    // Menu event handlers
    const openFileMenuItem = document.getElementById('openFileMenuItem');
    const exitMenuItem = document.getElementById('exitMenuItem');
    const aboutMenuItem = document.getElementById('aboutMenuItem');
    
    // Modal components
    const aboutModal = document.getElementById('aboutModal');
    const processingModal = document.getElementById('processingModal');
    const closeButtons = document.getElementsByClassName('close-button');
    
    // Status and progress components
    const statusLabel = document.getElementById('statusLabel');
    const progressBar = document.getElementById('progressBar');
      // Load saved OCR prompt and user text from localStorage if available
    const savedPrompt = localStorage.getItem('ocrPrompt');
    const savedUserText = localStorage.getItem('userText');
    
    if (savedPrompt) {
        ocrPromptInput.value = savedPrompt;
    }
    
    if (savedUserText) {
        userTextInput.value = savedUserText;
    }
      // Initialize the variables
    ocrPrompt = ocrPromptInput.value;
    userText = userTextInput.value;
    
    // Save settings when button is clicked
    savePromptButton.addEventListener('click', () => {
        ocrPrompt = ocrPromptInput.value;
        userText = userTextInput.value;
        localStorage.setItem('ocrPrompt', ocrPrompt);
        localStorage.setItem('userText', userText);
        updateStatus('OCR prompt configuration saved.', 'success');
        setTimeout(() => {
            updateStatus('Click "Upload Photo" button to upload a beer menu photo.', 'normal');
        }, 2000);
    });// Attach event listeners
    if (uploadButton) {
        // Add multiple event handlers to ensure the button works
        uploadButton.addEventListener('click', function(e) {
            console.log('Upload button clicked (addEventListener)');
            if (fileInput) {
                e.preventDefault();
                e.stopPropagation();
                fileInput.click();
            } else {
                console.error('File input not found');
            }
        });
        
        // Add handler for the text span inside the button
        const buttonText = uploadButton.querySelector('.button-text');
        if (buttonText) {
            buttonText.addEventListener('click', function(e) {
                console.log('Button text span clicked');
                if (fileInput) {
                    e.preventDefault();
                    e.stopPropagation();
                    fileInput.click();
                }
            });
        }
    } else {
        console.error('Upload button not found');
    }
    
    if (fileInput) {
        fileInput.addEventListener('change', function(event) {
            console.log('File input changed (addEventListener)');
            processImage(event);
        });
    }
    
    exitButton?.addEventListener('click', exitApplication);
    exitMenuItem?.addEventListener('click', exitApplication);
    
    openFileMenuItem.addEventListener('click', () => fileInput.click());
    aboutMenuItem.addEventListener('click', showAboutModal);
    
    // Close button for modals
    Array.from(closeButtons).forEach(button => {
        button.addEventListener('click', () => {
            aboutModal.style.display = 'none';
        });
    });
    
    // Click outside modal to close
    window.addEventListener('click', event => {
        if (event.target === aboutModal) {
            aboutModal.style.display = 'none';
        }
    });
    
    // Prevent closing processing modal when clicking on it
    processingModal.addEventListener('click', event => {
        if (event.target === processingModal) {
            event.stopPropagation();
        }
    });
      // Set initial status message
    updateStatus('Click "Upload Photo" button to upload a beer menu photo.', 'normal');
    
    console.log('Beer OCR Web application initialized');
}    // Add more verbose debugging
    function debug(message) {
        console.log(`DEBUG [${new Date().toISOString()}]: ${message}`);
    }

    // Add direct button debugging - add this early in the initialization
    const testButtonClick = function() {
        debug('Direct button test clicked');
        document.getElementById('fileInput')?.click();
    };

    // Make the test function globally available for console testing
    window.testButtonClick = testButtonClick;
    
    // Log important elements
    debug(`Upload button exists: ${!!document.getElementById('uploadButton')}`);
    debug(`File input exists: ${!!document.getElementById('fileInput')}`);
    
    // Add body click listener for delegation
    document.body.addEventListener('click', function(e) {
        if (e.target.id === 'uploadButton' || e.target.closest('#uploadButton')) {
            debug('Upload button clicked via body event delegation');
            document.getElementById('fileInput')?.click();
        }
    });
    
    debug('Added global click handlers for debugging');

// Process the uploaded image
async function processImage(event) {
    const file = event.target.files[0];
    if (!file) return;
      // Check file type
    const validTypes = ['image/jpeg', 'image/png', 'image/bmp'];
    if (!validTypes.includes(file.type)) {
        updateStatus('Error: Unsupported file type. Please select a JPG, PNG or BMP file.', 'error');
        return;
    }
    
    try {        // Start progress indicator
        showProgressBar(true);
        updateStatus('Processing... Please wait.', 'processing');
        showProcessingModal(true, 'Processing... Please wait.');
        
        // Start timing
        const startTime = performance.now();
        
        // Use FormData to send the file to the API
        const formData = new FormData();
        formData.append('image', file);
        
        // Extract text from image using API
        const extractedText = await extractTextFromImage(formData);
        
        // Stop timing
        const endTime = performance.now();
        const elapsedSeconds = ((endTime - startTime) / 1000).toFixed(2);
          // Update status
        updateStatus(`Process completed. Total time: ${elapsedSeconds} seconds. Loading results...`, 'processing');
        
        // Show results
        const rowCount = showBeerPrices(extractedText);
          // Update final status with row count
        if (rowCount > 0) {
            updateStatus(`Process completed. Total time: ${elapsedSeconds} seconds. Products found: ${rowCount}`, 'success');
        } else {
            updateStatus(`Process completed. Total time: ${elapsedSeconds} seconds. No data found!`, 'error');
        }
    } catch (error) {        console.error('Error processing image:', error);
        updateStatus(`Error during processing: ${error.message}`, 'error');
        
        // Show error in alert for better visibility
        alert(`Error: ${error.message}`);
    } finally {
        // Hide progress indicator
        showProgressBar(false);
        showProcessingModal(false);
        
        // Reset the file input to allow selecting the same file again
        event.target.value = '';
    }
}

// Extract text from image using backend API
async function extractTextFromImage(formData) {
    try {
        const apiUrl = '/api/ocr/extractTest'; // Using the extractTest endpoint that accepts a custom prompt
        
        // Add the OCR prompt to the form data
        if (ocrPrompt) {
            formData.append('prompt', ocrPrompt);
        }
        
        // Add the user text to the form data
        if (userText) {
            formData.append('userText', userText);
        }
        
        const response = await fetch(apiUrl, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API Error: ${response.status} - ${errorText}`);
        }
        
        const data = await response.json();
        return data.result || '';} catch (error) {
        console.error('API request failed:', error);
        throw new Error(`API request failed: ${error.message}`);
    }
}

// Parse and display beer prices in the results grid
function showBeerPrices(extractedText) {
    const resultsGrid = document.getElementById('resultsGrid').getElementsByTagName('tbody')[0];
    
    // Clear existing rows
    resultsGrid.innerHTML = '';    
    if (!extractedText || extractedText.trim() === '') {
        alert('No beer information found in the image.');
        return 0;
    }
    
    const lines = extractedText.split('\n');
    let rowCount = 0;
    
    lines.forEach(line => {
        // Skip header lines or empty lines
        if (line.includes('---') || 
            line.trim() === '' ||            line.toLowerCase().includes('beer name') ||
            line.toLowerCase().includes('price') ||
            line.includes('Product') ||
            line.includes('Price')) {
            return;
        }
        
        // Try different separators: space, dash, pipe
        const separators = [' - ', ': ', ' : ', '|', ' | ', '  ', ' '];
        let parts = null;
        
        for (const separator of separators) {
            parts = line.split(separator, 2);
            if (parts.length === 2) {
                // Check if the second part contains a price (has digits and possibly ₺ symbol)
                if (/\d/.test(parts[1])) {
                    break;
                }
            }
        }
        
        if (parts && parts.length === 2) {
            const beerName = parts[0].trim();
            let price = parts[1].trim();
            
            //// Ensure price has ₺ symbol
            //if (!price.includes('₺')) {
            //    price += ' ₺';
            //}
            
            // Create a new row and add to the grid
            const newRow = document.createElement('tr');
            const nameCell = document.createElement('td');
            const priceCell = document.createElement('td');
            
            nameCell.textContent = beerName;
            priceCell.textContent = price;
            
            newRow.appendChild(nameCell);
            newRow.appendChild(priceCell);
            resultsGrid.appendChild(newRow);
            
            rowCount++;
              // Update the status label with current count every 5 items
            if (rowCount % 5 === 0) {
                updateStatus(`Process completed. Loading results... (${rowCount} products found)`, 'processing');
            }
        }
    });
    
    return rowCount;
}

// Update the status label
function updateStatus(message, status = 'normal') {
    const statusLabel = document.getElementById('statusLabel');
    statusLabel.textContent = message;
    
    // Remove all status classes
    statusLabel.classList.remove('status-success', 'status-processing', 'status-error');
    
    // Add appropriate status class
    switch (status) {
        case 'success':
            statusLabel.classList.add('status-success');
            break;
        case 'processing':
            statusLabel.classList.add('status-processing');
            break;
        case 'error':
            statusLabel.classList.add('status-error');
            break;
    }
}

// Show or hide progress bar
function showProgressBar(visible) {
    const progressBar = document.getElementById('progressBar');
    progressBar.style.display = visible ? 'block' : 'none';
}

// Show the About modal
function showAboutModal() {
    const aboutModal = document.getElementById('aboutModal');
    aboutModal.style.display = 'block';
}

// Show or hide the processing modal
function showProcessingModal(visible, message = '') {
    const processingModal = document.getElementById('processingModal');
    const processingMessage = document.getElementById('processingMessage');
    
    if (visible) {
        processingMessage.textContent = message;
        processingModal.style.display = 'block';
    } else {
        processingModal.style.display = 'none';
    }
}

// Exit application - in web app, just show a confirmation
function exitApplication() {
    if (confirm('Are you sure you want to exit the application?')) {
        // In a real application, this might redirect to a home page
        // or perform cleanup tasks before closing
        window.close();
        
        // If window.close() doesn't work (common in modern browsers),
        // redirect to a landing page
        setTimeout(() => {
            window.location.href = '/';
        }, 100);
    }
}

// Prevent browser's default drag and drop behavior
document.addEventListener('dragover', event => {
    event.preventDefault();
});

document.addEventListener('drop', event => {
    event.preventDefault();
    
    // Handle file drop directly
    if (event.dataTransfer.items) {
        // Use DataTransferItemList interface
        for (let i = 0; i < event.dataTransfer.items.length; i++) {
            // If dropped item is an image file
            if (event.dataTransfer.items[i].kind === 'file') {
                const file = event.dataTransfer.items[i].getAsFile();
                // Create a change event for the fileInput
                const fileInput = document.getElementById('fileInput');
                
                // Store the file in a DataTransfer
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                fileInput.files = dataTransfer.files;
                
                // Trigger change event
                const event = new Event('change', { bubbles: true });
                fileInput.dispatchEvent(event);
                break;
            }
        }
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', event => {
    // Ctrl+O to open file
    if (event.ctrlKey && event.key === 'o') {
        event.preventDefault();
        document.getElementById('fileInput').click();
    }
    
    // Escape key to close modals
    if (event.key === 'Escape') {
        document.getElementById('aboutModal').style.display = 'none';
        // Don't close processing modal with Escape
    }
});

// Add direct button event listener at the end of the file to ensure it's attached
document.addEventListener('DOMContentLoaded', function() {
    const uploadBtn = document.getElementById('uploadButton');
    const fileInput = document.getElementById('fileInput');
    
    if (uploadBtn && fileInput) {
        console.log('Setting up direct button handler');
        uploadBtn.onclick = function() {
            console.log('Button clicked via direct onclick');
            fileInput.click();
            return false;
        };
    }
});
