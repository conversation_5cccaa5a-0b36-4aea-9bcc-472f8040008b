using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using PA.PAPA.API.Converters;
using PA.PAPA.API.Filters;
using PA.PAPA.API.Middleware;
using PA.PAPA.API.Models;
using PA.PAPA.API.Services;
using PA.PAPA.DTO.Options;

namespace PA.PAPA.API
{
    public class Startup
    {
        private readonly Security.Security _security;
        internal readonly UserClaimService UserClaimService;
        internal readonly UserService UserService;
        private UserClaimDatabaseSettings _userClaimDatabaseSettings;
        private UserDatabaseSettings _userDatabaseSettings;

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;

            InitUserClaimDbSettings();

            UserClaimService = new UserClaimService(_userClaimDatabaseSettings);
            UserService = new UserService(_userDatabaseSettings);
            _security = new Security.Security(this);
        }

        public IConfiguration Configuration { get; }

        private void InitUserClaimDbSettings()
        {
            _userClaimDatabaseSettings = new UserClaimDatabaseSettings
            {
                ConnectionString = Configuration.GetValue<string>("UserClaimDatabaseSettings:ConnectionString"),
                DatabaseName = Configuration.GetValue<string>("UserClaimDatabaseSettings:DatabaseName"),
                UserClaimsCollectionName =
                    Configuration.GetValue<string>("UserClaimDatabaseSettings:UserClaimsCollectionName")
            };

            _userDatabaseSettings = new UserDatabaseSettings
            {
                ConnectionString = Configuration.GetValue<string>("UserDatabaseSettings:ConnectionString"),
                DatabaseName = Configuration.GetValue<string>("UserDatabaseSettings:DatabaseName"),
                UserCollectionName = Configuration.GetValue<string>("UserDatabaseSettings:UserCollectionName")
            };
        }

        public void ConfigureServices(IServiceCollection services)
        {
            services.Configure<UserClaimDatabaseSettings>(
                Configuration.GetSection(nameof(UserClaimDatabaseSettings)));
            services.AddSingleton<IUserClaimDatabaseSettings>(sp => sp.GetRequiredService<IOptions<UserClaimDatabaseSettings>>().Value);

            services.Configure<UserDatabaseSettings>(
                Configuration.GetSection(nameof(UserDatabaseSettings)));
            services.AddSingleton<IUserDatabaseSettings>(sp => sp.GetRequiredService<IOptions<UserDatabaseSettings>>().Value);

            services.Configure<RoleDatabaseSettings>(
                Configuration.GetSection(nameof(RoleDatabaseSettings)));
            services.AddSingleton<IRoleDatabaseSettings>(sp => sp.GetRequiredService<IOptions<RoleDatabaseSettings>>().Value);
            services.AddSingleton<RoleService>();


            services.AddSingleton<UserService>();
            services.AddSingleton<UserClaimService>();
            services.AddSwaggerGen(c => { c.SwaggerDoc("v1", new OpenApiInfo {Title = "PAPA API", Version = "v1"}); });


            _security.InitSecurity(services, UserClaimService, UserService);


            services.AddControllers(opt => { opt.Filters.Add(typeof(ValidatorActionFilter)); })
                .AddNewtonsoftJson(opt =>
                    {
                        opt.SerializerSettings.Converters.Add(new TimeSpanFormatConverter());
                        opt.SerializerSettings.Converters.Add(new DateTimeFormatConverter());
                        opt.UseMemberCasing();
                    }
                );
            services.AddMemoryCache();

            services.AddOptions();
            services.Configure<ApiConfiguration>(Configuration.GetSection("ApiConfiguration"));

            DiRegister.InitDiRegister(services);
        }


        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseSwagger();
            app.UseSwaggerUI(c => { c.SwaggerEndpoint("/swagger/v1/swagger.json", "PAPA API V1"); });

            app.UseRouting();
            app.UseCors(builder =>
            {
                builder
                    .AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader();
            });

            // app.UseCors("CorsPolicy");
            if (env.IsDevelopment()) app.UseDeveloperExceptionPage();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseMiddleware(typeof(ErrorHandlingMiddleware));
            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });
        }
    }
}