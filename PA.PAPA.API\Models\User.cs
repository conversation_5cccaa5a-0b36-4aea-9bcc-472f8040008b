﻿#region snippet_NewtonsoftJsonImport

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

#endregion

namespace PA.PAPA.API.Models
{
    public class User
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }
        public int UserId { get; set; }

        [BsonElement("Name")]
        [JsonProperty("Name")]
        public string Name { get; set; }
        
        [BsonElement("UserName")]
        [JsonProperty("UserName")]
        public string UserName { get; set; }

        public string Email { get; set; }
        public string EmployeeNumber { get; set; }
        // public string Email { get; set; }

    }
}
