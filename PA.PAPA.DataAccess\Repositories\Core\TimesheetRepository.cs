﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using PA.PAPA.DataAccess.Repositories.Core.Interfaces;
using PA.PAPA.DTO.Core;

namespace PA.PAPA.DataAccess.Repositories.Core
{
    public class TimesheetRepository : ITimesheetRepository
    {
        private readonly IConnectionFactory _connectionFactory;

        public TimesheetRepository(IConnectionFactory connectionFactory) {
            _connectionFactory = connectionFactory;
        }

        public async Task<IEnumerable<TimesheetDTO>> Read(DateTime startDate, int employeeId) {
            var param = new DynamicParameters();
            param.Add("@EmployeeId", employeeId);
            param.Add("@StartDate", startDate);

            using (var conn = _connectionFactory.GetConnection) {
                return await conn.QueryAsync<TimesheetDTO>("Core.TimesheetSelect", param,
                    commandType: CommandType.StoredProcedure);
            }
        }

        public async Task<TimesheetDTO> Read(int id) {
            var param = new DynamicParameters();
            param.Add("@TimesheetId", id);

            using (var conn = _connectionFactory.GetConnection) {
                return await conn.QuerySingleAsync<TimesheetDTO>("Core.TimesheetSelect", param,
                    commandType: CommandType.StoredProcedure);
            }
        }

        //public async Task<IEnumerable<TimesheetDTO>> Save(IEnumerable<TimesheetDTO> dtos) {

        //    var ids = new List<int>();

        //    var dt = new DataTable();
        //    dt.Columns.Add("TimesheetId", typeof(int));
        //    dt.Columns.Add("EmployeeId", typeof(int));
        //    dt.Columns.Add("StartDate", typeof(DateTime));
        //    dt.Columns.Add("ProjectId", typeof(int));
        //    dt.Columns.Add("ProjectActivityId", typeof(int));
        //    dt.Columns.Add("Day1Hrs", typeof(decimal));
        //    dt.Columns.Add("Day2Hrs", typeof(decimal));
        //    dt.Columns.Add("Day3Hrs", typeof(decimal));
        //    dt.Columns.Add("Day4Hrs", typeof(decimal));
        //    dt.Columns.Add("Day5Hrs", typeof(decimal));
        //    dt.Columns.Add("Day6Hrs", typeof(decimal));
        //    dt.Columns.Add("Day7Hrs", typeof(decimal));
        //    dt.Columns.Add("Day1Note", typeof(string));
        //    dt.Columns.Add("Day2Note", typeof(string));
        //    dt.Columns.Add("Day3Note", typeof(string));
        //    dt.Columns.Add("Day4Note", typeof(string));
        //    dt.Columns.Add("Day5Note", typeof(string));
        //    dt.Columns.Add("Day6Note", typeof(string));
        //    dt.Columns.Add("Day7Note", typeof(string));
        //    dt.Columns.Add("UpdatedById", typeof(int));

        //    foreach (var dto in dtos) {
        //        var dr = dt.NewRow();
        //        dr["TimesheetId"] = dto.TimesheetId;
        //        dr["EmployeeId"] = dto.EmployeeId;
        //        dr["StartDate"] = dto.StartDate;
        //        dr["ProjectId"] = dto.ProjectId;
        //        dr["ProjectActivityId"] = dto.ProjectActivityId;

        //        dr["Day1Hrs"] = dto.Day1Hrs.DBValue();                
        //        dr["Day2Hrs"] = dto.Day2Hrs.DBValue();
        //        dr["Day3Hrs"] = dto.Day3Hrs.DBValue();
        //        dr["Day4Hrs"] = dto.Day4Hrs.DBValue();
        //        dr["Day5Hrs"] = dto.Day5Hrs.DBValue();
        //        dr["Day6Hrs"] = dto.Day6Hrs.DBValue();
        //        dr["Day7Hrs"] = dto.Day7Hrs.DBValue();
        //        dr["Day1Note"] = dto.Day1Note.DBValue();
        //        dr["Day2Note"] = dto.Day2Note.DBValue();
        //        dr["Day3Note"] = dto.Day3Note.DBValue();
        //        dr["Day4Note"] = dto.Day4Note.DBValue();
        //        dr["Day5Note"] = dto.Day5Note.DBValue();
        //        dr["Day6Note"] = dto.Day6Note.DBValue();
        //        dr["Day7Note"] = dto.Day7Note.DBValue();
        //        dr["UpdatedById"] = dto.UpdatedById;
        //        dt.Rows.Add(dr);
        //    }

        //    var param = new DynamicParameters();
        //    param.Add("@Timesheet", dt.AsTableValuedParameter("Core.udtTimesheet"));
        //    using (var multiResult = await _connectionFactory.GetConnection.QueryMultipleAsync(
        //        "Core.TimesheetSave", param, commandType: CommandType.StoredProcedure)) {

        //        ids = multiResult.Read<int>().ToList();
        //    }

        //    return dtos;
        //}


        public async Task<int> Create(TimesheetDTO dto) {
            var param = new DynamicParameters();

            param.Add("@TimesheetId", dto.TimesheetId, null, ParameterDirection.InputOutput);
            param.Add("@EmployeeId", dto.EmployeeId);
            param.Add("@StartDate", dto.StartDate);
            param.Add("@ProjectActivityId", dto.ProjectActivityId);
            param.Add("@Day1Hrs", dto.Day1Hrs);
            param.Add("@Day2Hrs", dto.Day2Hrs);
            param.Add("@Day3Hrs", dto.Day3Hrs);
            param.Add("@Day4Hrs", dto.Day4Hrs);
            param.Add("@Day5Hrs", dto.Day5Hrs);
            param.Add("@Day6Hrs", dto.Day6Hrs);
            param.Add("@Day7Hrs", dto.Day7Hrs);
            param.Add("@Day1Note", dto.Day1Note);
            param.Add("@Day2Note", dto.Day2Note);
            param.Add("@Day3Note", dto.Day3Note);
            param.Add("@Day4Note", dto.Day4Note);
            param.Add("@Day5Note", dto.Day5Note);
            param.Add("@Day6Note", dto.Day6Note);
            param.Add("@Day7Note", dto.Day7Note);
            param.Add("@UpdatedById", dto.UpdatedById);

            using (var conn = _connectionFactory.GetConnection) {
                await conn.ExecuteAsync("Core.TimesheetInsert", param, commandType: CommandType.StoredProcedure);
                dto.TimesheetId = param.Get<int>("@TimesheetId");
            }

            return dto.TimesheetId;
        }

        public async Task<int> Update(TimesheetDTO dto) {
            var param = new DynamicParameters();

            param.Add("@TimesheetId", dto.TimesheetId);
            param.Add("@ProjectActivityId", dto.ProjectActivityId);
            param.Add("@Day1Hrs", dto.Day1Hrs);
            param.Add("@Day2Hrs", dto.Day2Hrs);
            param.Add("@Day3Hrs", dto.Day3Hrs);
            param.Add("@Day4Hrs", dto.Day4Hrs);
            param.Add("@Day5Hrs", dto.Day5Hrs);
            param.Add("@Day6Hrs", dto.Day6Hrs);
            param.Add("@Day7Hrs", dto.Day7Hrs);
            param.Add("@Day1Note", dto.Day1Note);
            param.Add("@Day2Note", dto.Day2Note);
            param.Add("@Day3Note", dto.Day3Note);
            param.Add("@Day4Note", dto.Day4Note);
            param.Add("@Day5Note", dto.Day5Note);
            param.Add("@Day6Note", dto.Day6Note);
            param.Add("@Day7Note", dto.Day7Note);
            param.Add("@UpdatedById", dto.UpdatedById);

            using (var conn = _connectionFactory.GetConnection) {
                await conn.ExecuteAsync("Core.TimesheetUpdate", param, commandType: CommandType.StoredProcedure);
            }

            return dto.TimesheetId;
        }

        public async Task<int> Delete(int id) {
            var param = new DynamicParameters();
            param.Add("@TimesheetId", id);
            using (var conn = _connectionFactory.GetConnection) {
                return await _connectionFactory.GetConnection.ExecuteAsync("Core.TimesheetDelete", param, commandType: CommandType.StoredProcedure);
            }
        }

        public void Dispose() {
            _connectionFactory.Dispose();
        }

    }
}