﻿/*
 * © 2025 <PERSON> | SmartIT. All Rights Reserved.
 * https://johnkocer.github.io
 * Version: 1.0.0 Date: 05-21-2025
 */

using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.Net.Http.Headers;
using System.Diagnostics;
using NT.BIRAOCR.DTO;
using NT.BIRAOCR.Logic.Interfaces;
using Microsoft.AspNetCore.Http;

namespace NT.BIRAOCR.API.Controllers
{
  [ApiController]
  [Route("api/[controller]")]
  public class AnalysisController : ControllerBase
  {
    private readonly IAnalysisService _analysisService;
    private readonly IBlobStorageService _blobStorageService;

    public AnalysisController(IAnalysisService analysisService, IBlobStorageService blobStorageService)
    {
      _analysisService = analysisService;
      _blobStorageService = blobStorageService;
    }

    [HttpPost("submit")]
    public async Task<IActionResult> SubmitAnalysis([FromBody] AnalysisRequestDto request)
    {
      try
      {
        var result = await _analysisService.SubmitAnalysisRequestAsync(request);
        return Ok(new { result = result });
      }
      catch (Exception ex)
      {
        return BadRequest(new { error = ex.Message });
      }
    }

    [HttpPost("upload-image")]
    public async Task<IActionResult> UploadImage(IFormFile file)
    {
      if (file == null || file.Length == 0)
      {
        return BadRequest("No file uploaded");
      }

      // Validate file type (only allow images)
      string[] permittedExtensions = { ".jpg", ".jpeg", ".png", ".gif" };
      var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();

      if (string.IsNullOrEmpty(fileExtension) || !permittedExtensions.Contains(fileExtension))
      {
        return BadRequest("Invalid file type. Only jpg, jpeg, png, and gif files are allowed.");
      }

      try
      {
        // Upload the image using the blob storage service
        var imageUrl = await _blobStorageService.UploadFileAsync(file);

        return Ok(new { imageUrl = imageUrl });
      }
      catch (Exception ex)
      {
        return StatusCode(500, new { error = $"Image upload failed: {ex.Message}" });
      }
    }
  }
}