﻿using System;
using System.Data;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using PA.PAPA.DTO.Options;

namespace PA.PAPA.DataAccess
{
    public class ConnectionFactory : IConnectionFactory, IDisposable
    {
        private readonly IOptions<ApiConfiguration> _apiConfigs;
        private IDbConnection? _connection;

        public ConnectionFactory(IOptions<ApiConfiguration> apiConfigs)
        {
            _apiConfigs = apiConfigs;
        }

        public IDbConnection GetConnection
        {
            get
            {
                if (_connection == null) _connection = new SqlConnection(_apiConfigs.Value.DbConnectionString);
                if (_connection.State != ConnectionState.Open) _connection.Open();
                return _connection;
            }
        }

        public void CloseConnection()
        {
            if (_connection != null && _connection.State == ConnectionState.Open) _connection.Close();
        }

        public void Dispose()
        {
            //Debug.WriteLine("_connection.State", _connection.State);

            if (_connection != null && _connection.State != ConnectionState.Closed)
                _connection.Dispose();
        }
    }
}