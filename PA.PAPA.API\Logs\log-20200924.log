2020-09-24 02:57:41.401 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 02:57:41.858 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:04:20.179 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 08:04:20.884 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:24:00.415 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 08:24:28.975 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 08:24:31.603 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:24:37.139 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:24:37.140 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:24:37.140 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:25:44.691 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:25:44.691 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:26:50.069 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:26:50.069 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:33:57.852 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 08:34:28.526 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:35:01.717 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 08:35:32.917 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:36:22.383 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 08:36:52.430 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 08:36:53.153 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:37:00.432 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:37:00.433 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:37:00.433 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:37:00.433 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 08:37:00.433 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:18:21.682 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 09:18:38.013 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 09:18:38.322 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 09:18:53.187 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:51:55.454 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 09:51:55.777 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 09:52:09.602 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:52:09.602 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:52:09.603 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:52:09.603 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:53:10.884 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:53:10.884 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:53:10.885 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:53:10.884 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:54:12.458 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:54:12.458 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:54:22.181 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 09:54:22.802 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 09:54:23.156 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 09:54:30.014 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:54:31.829 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:54:33.648 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:56:20.599 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:56:20.599 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:57:50.010 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:57:50.010 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 09:58:00.672 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 09:58:07.250 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:42:03.212 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 10:42:35.759 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:45:30.175 -07:00 [Error] No route matches the supplied values.
2020-09-24 10:46:45.963 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 10:47:08.847 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 10:47:10.473 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 10:47:10.682 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 10:47:17.387 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:47:17.387 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:47:22.257 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:47:45.743 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:49:04.153 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 10:49:34.906 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:51:42.085 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 10:52:06.245 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 10:52:11.770 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:52:14.313 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:53:15.165 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 10:53:15.315 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 10:53:16.927 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 10:53:17.235 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 10:53:30.126 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:53:30.126 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:53:30.126 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:53:30.126 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 10:54:50.403 -07:00 [Error] No route matches the supplied values.
2020-09-24 10:59:10.107 -07:00 [Error] A write operation resulted in an error.
  E11000 duplicate key error collection: PapaSecurityDb.Roles index: _id_ dup key: { _id: ObjectId('5f690843d701347f8c7dfdea') }
2020-09-24 10:59:36.327 -07:00 [Error] No route matches the supplied values.
2020-09-24 11:02:08.747 -07:00 [Error] No route matches the supplied values.
2020-09-24 11:04:05.799 -07:00 [Error] No route matches the supplied values.
2020-09-24 11:05:46.794 -07:00 [Error] No route matches the supplied values.
2020-09-24 11:09:49.310 -07:00 [Error] No route matches the supplied values.
2020-09-24 11:25:08.786 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 11:25:26.758 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 11:25:27.149 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 11:25:40.236 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:25:40.270 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:25:40.270 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:25:40.354 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:25:40.447 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:25:40.676 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:25:40.958 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:29:44.220 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 11:29:51.905 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 11:29:51.921 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 11:29:55.879 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 11:29:56.192 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 11:30:10.889 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 11:30:11.200 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 11:30:15.328 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:30:15.329 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:30:15.329 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:30:15.339 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:30:15.339 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:31:16.472 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 11:31:21.864 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 11:31:21.913 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 11:31:25.556 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 11:31:27.625 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 11:31:40.563 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 11:31:42.633 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 11:31:45.922 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:31:45.922 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:31:45.926 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:31:45.943 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 11:31:45.943 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 14:05:18.317 -07:00 [Error] Error getting Employees: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 24
   at PA.PAPA.Logic.Admin.EmployeeLogic.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Admin\EmployeeLogic.cs:line 21
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 31
2020-09-24 14:05:49.852 -07:00 [Error] Error getting Employee Types: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.ReadEmployeeTypes() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 89
   at PA.PAPA.Logic.Admin.EmployeeLogic.ReadEmployeeTypes() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Admin\EmployeeLogic.cs:line 77
   at PA.PAPA.API.Controllers.EmployeeController.GetEmployeeTypes() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 82
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 15:07:08.716 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 15:07:23.871 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 15:07:28.122 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 15:07:40.067 -07:00 [Error] Error getting Timesheets: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
2020-09-24 19:48:37.018 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 19:48:50.010 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-09-24 19:48:56.941 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-09-24 19:49:09.845 -07:00 [Error] Error getting Projects: 
System.Data.SqlClient.SqlException (0x80131904): A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections. (provider: Named Pipes Provider, error: 40 - Could not open a connection to SQL Server)
 ---> System.ComponentModel.Win32Exception (53): The network path was not found.
   at System.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken)
   at System.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
ClientConnectionId:00000000-0000-0000-0000-000000000000
Error Number:53,State:0,Class:20
