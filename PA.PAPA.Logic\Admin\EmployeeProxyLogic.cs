﻿using System.Collections.Generic;
using System.Threading.Tasks;
using PA.PAPA.DataAccess.Repositories.Admin.Interfaces;
using PA.PAPA.DTO.Admin;
using PA.PAPA.Logic.Admin.Interfaces;

namespace PA.PAPA.Logic.Admin
{
    public class EmployeeProxyLogic : IEmployeeProxyLogic
    {
        private readonly IEmployeeProxyRepository _repo;

        public EmployeeProxyLogic(IEmployeeProxyRepository EmployeeProxyRepository) {
            _repo = EmployeeProxyRepository;
        }

        public async Task<IEnumerable<EmployeeProxyDTO>> ReadByParent(int parentEmployeeId) {
            return await _repo.ReadByParent(parentEmployeeId);
        }

        public async Task<EmployeeProxyDTO> Read(int id) {
            return await _repo.Read(id);
        }

        public async Task<EmployeeProxyDTO> Create(EmployeeProxyDTO dto) {
            //TODO: Add data validation and other business logic

            return await _repo.Create(dto);
        }

        public async Task<EmployeeProxyDTO> Update(EmployeeProxyDTO dto) {
            var existing = await _repo.Read(dto.EmployeeProxyId);
            if (existing == default(EmployeeProxyDTO)) throw new NotFoundException();

            return await _repo.Update(dto);
        }

        //// TODO DELETE
        //public bool DuplicateExists(int organizationId, string EmployeeName)
        //{
        //    throw new NotImplementedException();
        //}

        public void Dispose() {
            // _repo?.Dispose();
        }
    }
}