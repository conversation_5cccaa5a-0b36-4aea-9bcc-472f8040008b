﻿namespace PA.PAPA.DTO.Options
{
    public class ApiConfiguration
    {
        public string DbConnectionString { get; set; }
        public string IdentityDbString { get; set; }
		public string ReportServiceUrl { get; set; }
		public string FinancialServiceUrl { get; set; }
		public string EmailServiceUrl { get; set; }
		public string ClientId { get; set; }
		public string SupportEmail { get; set; }
	}
}