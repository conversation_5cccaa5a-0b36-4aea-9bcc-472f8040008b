2020-10-22 16:38:42.198 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-10-22 16:38:42.198 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-10-22 16:39:07.359 -07:00 [Error] Error getting Employee Types: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.ReadEmployeeTypes() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 89
   at PA.PAPA.Logic.Admin.EmployeeLogic.ReadEmployeeTypes() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Admin\EmployeeLogic.cs:line 77
   at PA.PAPA.API.Controllers.EmployeeController.GetEmployeeTypes() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 82
2020-10-22 16:39:07.364 -07:00 [Error] Error getting Employees: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Admin.EmployeeRepository.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Admin\EmployeeRepository.cs:line 24
   at PA.PAPA.Logic.Admin.EmployeeLogic.Read() in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Admin\EmployeeLogic.cs:line 21
   at PA.PAPA.API.Controllers.EmployeeController.Get() in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\EmployeeController.cs:line 31
2020-10-22 16:44:30.206 -07:00 [Error] An error occurred while deserializing the EmployeeNumber property of class BooksApi.Models.UserClaim: Cannot deserialize a 'String' from BsonType 'Int32'.
2020-10-22 18:47:38.772 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-10-22 18:47:39.003 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-10-22 18:50:37.226 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-10-22 18:50:37.492 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-10-22 18:51:14.514 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-10-22 18:51:14.683 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-10-22 18:53:25.598 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-10-22 18:53:25.799 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
2020-10-22 18:55:53.005 -07:00 [Error] Error getting Projects: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.ProjectRepository.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\ProjectRepository.cs:line 89
   at PA.PAPA.Logic.Core.ProjectLogic.ReadEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\ProjectLogic.cs:line 84
   at PA.PAPA.API.Controllers.ProjectController.GetEmployeeProjectActivities(Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\ProjectController.cs:line 207
2020-10-22 18:55:53.191 -07:00 [Error] Error getting Timesheets: 
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at System.Data.SqlClient.SqlConnection.PermissionDemand()
   at System.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   at System.Data.SqlClient.SqlConnection.Open()
   at PA.PAPA.DataAccess.ConnectionFactory.get_GetConnection() in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\ConnectionFactory.cs:line 24
   at PA.PAPA.DataAccess.Repositories.Core.TimesheetRepository.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.DataAccess\Repositories\Core\TimesheetRepository.cs:line 25
   at PA.PAPA.Logic.Core.TimesheetLogic.Read(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.Logic\Core\TimesheetLogic.cs:line 23
   at PA.PAPA.API.Controllers.TimesheetController.Get(DateTime startDate, Int32 employeeId) in C:\Projects\Repos\PAPA_API\PA.PAPA.API\Controllers\TimesheetController.cs:line 35
