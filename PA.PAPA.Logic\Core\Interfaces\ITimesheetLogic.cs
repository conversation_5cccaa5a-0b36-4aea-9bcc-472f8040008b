﻿using PA.PAPA.DTO.Core;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace PA.PAPA.Logic.Core.Interfaces
{
    public interface ITimesheetLogic : IDisposable
    {
        Task<IEnumerable<TimesheetDTO>> Read(DateTime startDate, int employeeId);
        Task<TimesheetDTO> Read(int id);
        //Task<IEnumerable<TimesheetDTO>> Save(IEnumerable<TimesheetDTO> dtos);
        Task<int> Update(Dictionary<string, string> dto);
        Task<int> Create(Dictionary<string, string> dto);
        Task<int> Delete(int id);
    }
}
