﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PA.PAPA.DTO.Admin;

namespace PA.PAPA.DataAccess.Repositories.Admin.Interfaces
{
    public interface IEmployeeRepository : IDisposable
    {
        Task<IEnumerable<EmployeeDTO>> Read();
        Task<EmployeeDTO> Read(int id); 
        Task<EmployeeDTO> Create(EmployeeDTO dto);
        Task<EmployeeDTO> Update(EmployeeDTO dto);

        Task<IEnumerable<EmployeeTypeDTO>> ReadEmployeeTypes();
        // Task<bool> DuplicateExists(int organizationId, string EmplopyeeName);
    }
}